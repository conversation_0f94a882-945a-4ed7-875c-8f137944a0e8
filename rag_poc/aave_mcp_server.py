from fastmcp import FastMCP

# Initialize the MCP server
mcp = FastMCP("AaveMCPServer")

class AaveMCPServer:
    @mcp.tool()
    def query_token_balance(user_address: str, lending_pool_address: str) -> int:
        """
        Query the token balance of a user in the Aave lending pool.

        Args:
            user_address (str): The Ethereum address of the user.
            lending_pool_address (str): The Ethereum address of the Aave lending pool.

        Returns:
            int: The token balance of the user.
        """
        return 100

    @mcp.tool()
    def provide_liquidity(user_address: str, lending_pool_address: str, token_amount_map: dict) -> bool:
        """
        Provide liquidity to the Aave lending pool.

        Args:
            user_address (str): The Ethereum address of the user.
            lending_pool_address (str): The Ethereum address of the Aave lending pool.
            token_amount_map (dict): A dictionary mapping token addresses to the amount of tokens to provide.

        Returns:
            bool: True if the liquidity is provided successfully.
        """
        return True

if __name__ == '__main__':
    # mcp.run(transport="streamable-http", host="0.0.0.0", port=8000)
    mcp.run(transport="stdio")