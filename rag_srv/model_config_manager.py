#!/usr/bin/env python
"""
模型配置管理器
负责管理当前的embedding和LLM模型配置，支持运行时切换
"""

import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass
from langchain_ollama import OllamaEmbeddings, ChatOllama
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI

from config import (
    EMBEDDING_PROVIDER, LLM_PROVIDER,
    OLLAMA_BASE_URL, OLLAMA_EMBEDDING_MODEL, OLLAMA_LLM_MODEL,
    GOOGLE_API_KEY, GOOGLE_EMBEDDING_MODEL, GOOGLE_LLM_MODEL
)


@dataclass
class EmbeddingConfig:
    """Embedding模型配置"""
    provider: str  # 'ollama' 或 'gemini'
    model_name: str
    base_url: Optional[str] = None  # 仅ollama需要
    api_key: Optional[str] = None   # 仅gemini需要


@dataclass 
class LLMConfig:
    """LLM模型配置"""
    provider: str  # 'ollama' 或 'gemini'
    model_name: str
    base_url: Optional[str] = None  # 仅ollama需要
    api_key: Optional[str] = None   # 仅gemini需要


class ModelConfigManager:
    """模型配置管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._lock = threading.Lock()
        
        # 初始化默认配置
        self._embedding_config = EmbeddingConfig(
            provider=EMBEDDING_PROVIDER.lower(),
            model_name=OLLAMA_EMBEDDING_MODEL if EMBEDDING_PROVIDER.lower() == 'ollama' else GOOGLE_EMBEDDING_MODEL,
            base_url=OLLAMA_BASE_URL if EMBEDDING_PROVIDER.lower() == 'ollama' else None,
            api_key=GOOGLE_API_KEY if EMBEDDING_PROVIDER.lower() == 'gemini' else None
        )
        
        self._llm_config = LLMConfig(
            provider=LLM_PROVIDER.lower(),
            model_name=OLLAMA_LLM_MODEL if LLM_PROVIDER.lower() == 'ollama' else GOOGLE_LLM_MODEL,
            base_url=OLLAMA_BASE_URL if LLM_PROVIDER.lower() == 'ollama' else None,
            api_key=GOOGLE_API_KEY if LLM_PROVIDER.lower() == 'gemini' else None
        )
        
        self._initialized = True
    
    def get_embedding_config(self) -> EmbeddingConfig:
        """获取当前embedding配置"""
        with self._lock:
            return self._embedding_config
    
    def get_llm_config(self) -> LLMConfig:
        """获取当前LLM配置"""
        with self._lock:
            return self._llm_config
    
    def set_embedding_config(self, provider: str, model_name: str, 
                           base_url: Optional[str] = None, 
                           api_key: Optional[str] = None) -> bool:
        """设置embedding配置"""
        try:
            with self._lock:
                self._embedding_config = EmbeddingConfig(
                    provider=provider.lower(),
                    model_name=model_name,
                    base_url=base_url,
                    api_key=api_key
                )
            return True
        except Exception as e:
            print(f"设置embedding配置失败: {e}")
            return False
    
    def set_llm_config(self, provider: str, model_name: str,
                      base_url: Optional[str] = None,
                      api_key: Optional[str] = None) -> bool:
        """设置LLM配置"""
        try:
            with self._lock:
                self._llm_config = LLMConfig(
                    provider=provider.lower(),
                    model_name=model_name,
                    base_url=base_url,
                    api_key=api_key
                )
            return True
        except Exception as e:
            print(f"设置LLM配置失败: {e}")
            return False
    
    def create_embedding_instance(self):
        """根据当前配置创建embedding实例"""
        config = self.get_embedding_config()
        
        if config.provider == 'gemini':
            if not config.api_key:
                raise ValueError("Gemini embedding需要API密钥")
            return GoogleGenerativeAIEmbeddings(
                model=config.model_name,
                google_api_key=config.api_key
            )
        elif config.provider == 'ollama':
            if not config.base_url:
                raise ValueError("Ollama embedding需要base_url")
            return OllamaEmbeddings(
                base_url=config.base_url,
                model=config.model_name
            )
        else:
            raise ValueError(f"不支持的embedding提供商: {config.provider}")
    
    def create_llm_instance(self, callbacks=None, verbose=False, temperature=0.2):
        """根据当前配置创建LLM实例"""
        config = self.get_llm_config()
        
        if config.provider == 'gemini':
            if not config.api_key:
                raise ValueError("Gemini LLM需要API密钥")
            return ChatGoogleGenerativeAI(
                model=config.model_name,
                google_api_key=config.api_key,
                callbacks=callbacks,
                verbose=verbose,
                temperature=temperature
            )
        elif config.provider == 'ollama':
            if not config.base_url:
                raise ValueError("Ollama LLM需要base_url")
            return ChatOllama(
                base_url=config.base_url,
                model=config.model_name,
                callbacks=callbacks,
                verbose=verbose
            )
        else:
            raise ValueError(f"不支持的LLM提供商: {config.provider}")
    
    def get_collection_name(self, base_name: str = "documents") -> str:
        """根据当前embedding配置生成collection名称"""
        config = self.get_embedding_config()
        # 将模型名中的特殊字符替换为-
        safe_model_name = config.model_name.replace(":", "-").replace("/", "-").replace("-", "-")
        return f"{base_name}_{config.provider}_{safe_model_name}"
    
    def get_config_info(self) -> Dict[str, Any]:
        """获取当前配置信息"""
        embedding_config = self.get_embedding_config()
        llm_config = self.get_llm_config()
        
        return {
            "embedding": {
                "provider": embedding_config.provider,
                "model_name": embedding_config.model_name,
                "collection_name": self.get_collection_name(),
                "base_url": embedding_config.base_url,
                "api_key": embedding_config.api_key,
            },
            "llm": {
                "provider": llm_config.provider,
                "model_name": llm_config.model_name,
                "base_url": llm_config.base_url,
                "api_key": llm_config.api_key,
            }
        }


# 全局单例实例
model_config_manager = ModelConfigManager()
