version: 1

apps:
  - appID: mcp_colpali
    appDirPath: ../
    appPort: 11000
    command: ["node", "build/index_http.js"]
    #resourcesPath: "./dapr/components"

    env:
      PORT: 11000
      API_URI: "http://10.1.177.121:8080"
      
    # Dapr sidecar配置
    daprHTTPPort: 6500
    daprGRPCPort: 65000
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: debug
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 12090
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3