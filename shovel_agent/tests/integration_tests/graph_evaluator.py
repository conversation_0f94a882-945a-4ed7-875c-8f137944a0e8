import asyncio

from langgraph.constants import CONF, CONFIG_KEY_THREAD_ID
import uuid
from openevals.llm import create_llm_as_judge
from openevals.prompts import CORRECTNESS_PROMPT

from shovel_agent.llms import get_llm
from shovel_agent.graph import builder

correctness_evaluator = create_llm_as_judge(
    # CONCISENESS_PROMPT is just an f-string
    prompt=CORRECTNESS_PROMPT,
    feedback_key="correctness",
    judge=get_llm(),
)

graph = builder.compile()
async def main():
    config = {
            CONF: {},
            "user_id": "tester",
        }


    inputs = "How much has the price of doodads changed in the past year?"
    reference_outputs = "The price of doodads has decreased by 50% in the past year."
    result = await graph.ainvoke(
        {"messages": [{"role": "user", "content": inputs}]},
        {**config, CONFIG_KEY_THREAD_ID: str(uuid.uuid4())},
    )
    outputs = result["messages"][-1].content
    print(f"outputs: {outputs}")

    eval_result = correctness_evaluator(
      inputs=inputs,
      outputs=outputs,
      reference_outputs=reference_outputs
    )
    print(eval_result)
if __name__ == "__main__":
    asyncio.run(main())
