from typing import Annotated

from langchain_chroma import Chroma
from langchain.tools.retriever import create_retriever_tool
from langchain_core.tools import tool
from chromadb import HttpClient
from dotenv import load_dotenv
import re

from langgraph.prebuilt import InjectedState

from .llms import get_embeddings
from .utils import get_last_user_message

# 导入Document类
try:
    from langchain.schema import Document
except ImportError:
    try:
        from langchain_core.documents import Document
    except ImportError:
        # 如果都导入失败，创建一个简单的Document类
        class Document:
            def __init__(self, page_content: str, metadata: dict = None):
                self.page_content = page_content
                self.metadata = metadata or {}

import os

def create_embeddings():
    import asyncio
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    async def co_wrap():
        return get_embeddings()
    return asyncio.run(co_wrap())

load_dotenv()
embeddings = create_embeddings()
# 尝试创建客户端
chroma_client = Chroma(collection_name=os.getenv('CHROMA_COLLECTION_NAME'), persist_directory=os.getenv('CHROMA_PERSIST_DIR'), embedding_function=embeddings, client=HttpClient(host=os.getenv('CHROMA_HOST')))

# 混合搜索配置
HYBRID_SEARCH_MAX_KEYWORDS = int(os.getenv('HYBRID_SEARCH_MAX_KEYWORDS', '5'))  # 最大关键词数量
HYBRID_SEARCH_MIN_KEYWORD_LENGTH = int(os.getenv('HYBRID_SEARCH_MIN_KEYWORD_LENGTH', '2'))  # 最小关键词长度


def extract_keywords_from_query(question):
    """从用户问题中提取关键词用于混合搜索"""

    # 移除常见的停用词
    stop_words = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '什么', '怎么', '为什么', '如何', '请', '能', '可以', '吗', '呢', '吧', '啊', '哦', '嗯',
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'what', 'how', 'why', 'when', 'where', 'who', 'which'
    }

    # 提取中英文关键词
    # 匹配中文词汇（配置的最小长度以上）和英文单词（3个字符以上）
    min_length = HYBRID_SEARCH_MIN_KEYWORD_LENGTH
    chinese_words = re.findall(rf'[\u4e00-\u9fff]{{{min_length},}}', question)
    english_words = re.findall(r'[a-zA-Z]{3,}', question)

    # 过滤停用词
    keywords = []
    for word in chinese_words + english_words:
        if word.lower() not in stop_words:
            keywords.append(word)

    return keywords[:HYBRID_SEARCH_MAX_KEYWORDS]  # 使用配置的最大关键词数量


class ChromaEmbeddingWrapper:
    """ChromaDB兼容的embedding函数包装器"""

    def __init__(self, langchain_embedding):
        self.langchain_embedding = langchain_embedding

    def _ensure_numpy_array(self, data):
        """确保数据是numpy数组格式（ChromaDB内部需要）"""
        try:
            import numpy as np

            # 如果已经是numpy数组，直接返回
            if isinstance(data, np.ndarray):
                return data

            # 如果有tolist方法，说明可能是tensor等，先转换为列表再转numpy
            if hasattr(data, 'tolist'):
                list_data = data.tolist()
                return np.array(list_data, dtype=np.float32)

            # 如果是列表或其他可迭代对象，转换为numpy数组
            elif hasattr(data, '__iter__') and not isinstance(data, (str, bytes)):
                return np.array(list(data), dtype=np.float32)

            else:
                # 尝试强制转换
                return np.array(data, dtype=np.float32)

        except ImportError:
            # 如果没有numpy，回退到列表格式
            print("警告: numpy不可用，使用列表格式")
            return self._ensure_list_fallback(data)
        except Exception as e:
            import traceback
            print(f"数据类型转换为numpy数组失败:")
            print(f"  数据类型: {type(data)}")
            print(f"  数据值: {str(data)[:100]}...")
            print(f"  错误信息: {str(e)}")
            traceback.print_exc()
            raise ValueError(f"Cannot convert {type(data)} to numpy array: {str(e)}")

    def _ensure_list_fallback(self, data):
        """回退方案：确保数据是普通的Python列表格式"""
        # 如果有tolist方法（numpy数组、tensor等），使用它
        if hasattr(data, 'tolist'):
            return data.tolist()
        # 如果是其他可迭代对象，转换为列表
        elif hasattr(data, '__iter__') and not isinstance(data, (str, bytes)):
            return list(data)
        # 如果已经是列表，直接返回
        elif isinstance(data, list):
            return data
        else:
            # 尝试强制转换
            try:
                return list(data)
            except (TypeError, ValueError) as e:
                raise ValueError(f"Cannot convert {type(data)} to list: {str(e)}")

    def __call__(self, input):
        """ChromaDB期望的调用签名"""
        try:
            if isinstance(input, str):
                # 单个文本embedding
                print(f"处理单个文本embedding: {input[:50]}...")
                result = self.langchain_embedding.embed_query(input)
                print(f"原始embedding结果类型: {type(result)}")
                converted_result = self._ensure_numpy_array(result)
                print(f"转换后embedding类型: {type(converted_result)}, 形状: {converted_result.shape}")
                return converted_result

            elif isinstance(input, list):
                # 文本列表embedding
                print(f"处理文本列表embedding，数量: {len(input)}")
                results = self.langchain_embedding.embed_documents(input)
                print(f"原始embeddings结果类型: {type(results)}, 数量: {len(results)}")

                # 确保返回的是numpy数组的列表
                processed_results = []
                for i, result in enumerate(results):
                    print(f"处理第{i+1}个embedding，类型: {type(result)}")
                    converted_result = self._ensure_numpy_array(result)
                    processed_results.append(converted_result)

                print(f"所有embeddings转换完成，总数: {len(processed_results)}")
                return processed_results
            else:
                raise ValueError(f"Unsupported input type: {type(input)}")

        except Exception as e:
            import traceback
            import sys

            # 获取详细的异常信息
            exc_type, exc_value, exc_traceback = sys.exc_info()
            tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)

            print(f"ChromaEmbeddingWrapper调用失败:")
            print(f"  输入类型: {type(input)}")
            print(f"  输入内容: {input if isinstance(input, str) else f'列表长度: {len(input)}'}")
            print(f"  错误类型: {exc_type.__name__}")
            print(f"  错误信息: {str(e)}")
            print(f"  错误堆栈:")
            for line in tb_lines:
                print(f"    {line.rstrip()}")

            raise


# 创建基础的retriever
base_retriever = chroma_client.as_retriever(
    #search_type=os.getenv('SEARCH_TYPE', 'similarity'),
    search_kwargs={'k': 5}
    # search_kwargs={
    #     'k': int(os.getenv('SEARCH_TOP_K', 1)),
    #     'score_threshold': float(os.getenv('SEARCH_SCORE_THRESHOLD', 0.2))
    # }
)


def hybrid_search(vector_db, question, k=10, keywords=None, use_keyword_filter=True, score_threshold=None):
    """执行混合搜索：相似度搜索和关键词搜索各搜K个结果，合并去重后返回

    Args:
        vector_db: 向量数据库实例
        question: 查询问题
        k: 返回文档数量
        keywords: 关键词列表
        use_keyword_filter: 是否使用关键词过滤
        score_threshold: 相似度分数阈值，低于此分数的文档将被过滤掉
    """

    if not use_keyword_filter or not keywords:
        # 如果不使用关键词过滤或没有关键词，执行标准相似性搜索
        print("执行标准相似性搜索")
        docs_with_scores = vector_db.similarity_search_with_score(question, k=k)

        # 将分数添加到文档元数据中，并根据阈值过滤
        similarity_docs = []
        for doc, score in docs_with_scores:
            doc.metadata['similarity_score'] = score
            print(f"文档相似度分数: {score:.4f}")

            # 如果设置了分数阈值，则过滤低分数文档
            if score_threshold is None or score >= score_threshold:
                similarity_docs.append(doc)
            else:
                print(f"文档分数 {score:.4f} 低于阈值 {score_threshold}，已过滤")

        print(f"过滤后保留 {len(similarity_docs)} 个文档")
        return similarity_docs

    print(f"执行混合搜索，关键词: {keywords}")

    # 第一步：执行相似性搜索（搜索K个结果）
    print("第一步：执行相似性搜索")
    docs_with_scores = vector_db.similarity_search_with_score(question, k=k)

    # 将分数添加到文档元数据中，并根据阈值过滤
    similarity_docs = []
    for doc, score in docs_with_scores:
        doc.metadata['similarity_score'] = score
        print(f"相似度搜索文档分数: {score:.4f}")

        # 如果设置了分数阈值，则过滤低分数文档
        if score_threshold is None or score >= score_threshold:
            similarity_docs.append(doc)
        else:
            print(f"相似度搜索文档分数 {score:.4f} 低于阈值 {score_threshold}，已过滤")

    print(f"相似性搜索找到 {len(similarity_docs)} 个文档（过滤后）")

    # 第二步：执行关键词搜索（也搜索K个结果）
    print("第二步：执行关键词搜索")

    try:
        # 尝试使用ChromaDB的原生查询进行关键词搜索
        # 首先获取embedding函数和客户端
        langchain_embedding = vector_db._embedding_function
        chroma_client_instance = vector_db._client
        collection_name = vector_db._collection.name

        # 创建ChromaDB兼容的embedding函数包装器
        print(f"创建ChromaDB兼容的embedding函数包装器...")
        chroma_embedding = ChromaEmbeddingWrapper(langchain_embedding)

        # 重新获取collection并绑定embedding函数
        print(f"重新获取collection '{collection_name}' 并绑定embedding函数...")
        collection = chroma_client_instance.get_collection(
            name=collection_name,
            embedding_function=chroma_embedding
        )

        # 构建关键词过滤条件（不区分大小写）
        # 通过生成大小写变体来实现不区分大小写的匹配
        keyword_conditions = []
        for keyword in keywords:
            # 为每个关键词生成常见的大小写变体
            keyword_variants = []

            # 添加原始关键词
            keyword_variants.append(keyword)

            # 添加全小写
            if keyword.lower() != keyword:
                keyword_variants.append(keyword.lower())

            # 添加全大写
            if keyword.upper() != keyword:
                keyword_variants.append(keyword.upper())

            # 添加首字母大写
            if keyword.capitalize() != keyword:
                keyword_variants.append(keyword.capitalize())

            # 为每个变体创建$contains条件
            variant_conditions = []
            for variant in keyword_variants:
                variant_conditions.append({"$contains": variant})

            # 如果只有一个变体，直接使用；否则用$or组合
            if len(variant_conditions) == 1:
                keyword_conditions.append(variant_conditions[0])
            else:
                keyword_conditions.append({"$or": variant_conditions})

        where_document = None
        if len(keyword_conditions) == 1:
            where_document = keyword_conditions[0]
        elif len(keyword_conditions) > 1:
            where_document = {"$or": keyword_conditions}

        print(f"执行ChromaDB原生关键词查询，过滤条件: {where_document}")

        # 执行关键词查询，搜索K个结果
        results = collection.query(
            query_texts=[question],
            n_results=k,  # 搜索K个结果
            where_document=where_document
        )

        # 转换为LangChain Document格式
        keyword_docs = []
        if results['documents'] and results['documents'][0]:
            for i, doc_content in enumerate(results['documents'][0]):
                metadata = results['metadatas'][0][i] if results['metadatas'] and results['metadatas'][0] else {}
                doc_id = results['ids'][0][i] if results['ids'] and results['ids'][0] else f"doc_{i}"

                doc = Document(
                    page_content=doc_content,
                    metadata={**metadata, 'id': doc_id}
                )
                keyword_docs.append(doc)

        print(f"关键词搜索找到 {len(keyword_docs)} 个文档")

        # 第三步：合并两个搜索结果并去重
        print("第三步：合并相似性搜索和关键词搜索结果")

        # 先添加相似性搜索结果
        final_docs = list(similarity_docs)

        # 建立已有文档的ID集合用于去重
        existing_ids = set()
        for doc in similarity_docs:
            doc_id = doc.metadata.get('id', '')
            if doc_id:
                existing_ids.add(doc_id)
            else:
                # 如果没有ID，使用内容的hash作为标识
                existing_ids.add(hash(doc.page_content))

        # 添加关键词搜索结果（去重）
        added_count = 0
        for keyword_doc in keyword_docs:
            doc_id = keyword_doc.metadata.get('id', '')
            doc_identifier = doc_id if doc_id else hash(keyword_doc.page_content)

            if doc_identifier not in existing_ids:
                final_docs.append(keyword_doc)
                existing_ids.add(doc_identifier)
                added_count += 1

        print(f"相似性搜索: {len(similarity_docs)} 个文档")
        print(f"关键词搜索: {len(keyword_docs)} 个文档")
        print(f"去重后新增: {added_count} 个文档")
        print(f"合并后总计: {len(final_docs)} 个文档")

        # 返回前K个文档（如果总数超过K的话）
        print(f"最终返回: {len(final_docs)} 个文档")

        return final_docs

    except Exception as e:
        import traceback
        import sys

        # 获取详细的异常信息
        exc_type, exc_value, exc_traceback = sys.exc_info()

        # 获取异常发生的行号和文件名
        tb_lines = traceback.format_exception(exc_type, exc_value, exc_traceback)

        print(f"关键词搜索失败，返回相似性搜索结果:")
        print(f"  错误类型: {exc_type.__name__}")
        print(f"  错误信息: {str(e)}")

        # 找到最相关的错误行（在当前文件中的）
        current_file = __file__
        for line in tb_lines:
            if current_file in line or 'retriever.py' in line:
                print(f"  错误位置: {line.strip()}")

        # 打印完整的traceback用于调试
        print("  完整错误堆栈:")
        for line in tb_lines:
            print(f"    {line.rstrip()}")

        # 如果关键词搜索失败，返回已有的相似性搜索结果
        print(f"返回 {len(similarity_docs)} 个相似性搜索结果")
        return similarity_docs

@tool(parse_docstring=True)
def retrieve_defi_knowledge(messages: Annotated[list, InjectedState("messages")], use_hybrid_search: bool = True) -> str:
    """
    Search and return DeFi knowledge from the knowledge base using hybrid search. This tool searches for specific DeFi project information.

    Args:
        messages (list): Inject at runtime, does not need to assign value。

    Returns:
        str: Retrieved DeFi knowledge or indication if no relevant information found.
    """
    # 获取最后一个人工输入的消息
    query = get_last_user_message(messages)
    if not query or query.strip() == "":
        return "Error: Query cannot be empty."

    try:
        # 提取关键词用于混合搜索
        keywords = extract_keywords_from_query(query) if use_hybrid_search else []

        # 执行混合搜索或标准相似性搜索
        docs = hybrid_search(
            vector_db=chroma_client,
            question=query,
            k=5,
            keywords=keywords,
            use_keyword_filter=use_hybrid_search
        )

        if not docs:
            return f"No specific information found in knowledge base for '{query}'. You may use your general DeFi knowledge to answer this question."

        # 检查检索结果的相关性
        # 如果使用similarity_score_threshold，低分数的结果可能不够相关
        # if hasattr(docs[0], 'metadata') and 'score' in docs[0].metadata:
        #     score = docs[0].metadata.get('score', 0)
        #     if score < 0.3:  # 可调整的阈值
        #         return f"Found limited relevant information in knowledge base for '{query}'. Consider using general DeFi knowledge to provide a comprehensive answer."

        # 格式化返回结果
        result_content = []
        for i, doc in enumerate(docs, 1):
            content = doc.page_content.strip()
            if content:
                result_content.append(f"Document {i}: {content}")

        if result_content:
            return "\n\n".join(result_content)
        else:
            return f"No relevant content found in knowledge base for '{query}'. You may use your general DeFi knowledge to answer this question."

    except Exception as e:
        return f"Error retrieving information: {str(e)}. Please use your general DeFi knowledge to answer the question."

# 保持原有的retriever_tool作为备用
retriever_tool = retrieve_defi_knowledge

# @tool(parse_docstring=True)
# def retriever_tool(query: str) -> str:
#     """
#     专业的DeFi中文知识库
#
#     Args:
#         query (str): 用户要查询的问题。要求为中文
#
#     Returns:
#         str: 用户问题相关的文档。
#
#     """
#     retriever = create_retriever_tool(
#         chroma_store.as_retriever(search_type='similarity_score_threshold',search_kwargs={'k': 3,'score_threshold':0.5}),
#         "retrieve_defi_knowledge",
#         "专业的DeFi中文知识库, query参数请翻译为中文",
#         # "Search and return defi Knowledge, translate query to chinese before searching.",
#         # response_format="content_and_artifact"
#     )
#     return retriever.invoke(query)