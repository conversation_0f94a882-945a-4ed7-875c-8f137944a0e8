# mcp_config.py

MCP_SERVER_CONFIG = {
    # "AaveMCPServer": {
    #     # make sure you start your weather server on port 8000
    #     "url": "http://localhost:8000/mcp",
    #     "transport": "streamable_http",
    # },
    # "AaveMCPServer": {
    #     "command": "uv",
    #     # Replace with absolute path to your mcp_server.py file
    #     "args": ["run","src/shovel_agent/protocol/aave_mcp_server.py"],
    #     "transport": "stdio",
    # },
    "mariadb-mcp-server": {
        "url": "http://127.0.0.1:9001/sse",
        "transport": "sse"
    },
    "search1api": {
      "transport": "stdio",
      "command": "npx",
      "args": [
        "-y",
        "search1api-mcp"
      ],
      "env": {
        "SEARCH1API_KEY": "3C6D5DE7-832D-44F4-A001-1F0DC6484B31"
      }
    },
    # "crypto-moralis": {
    #   "transport": "stdio",
    #   "command": "node",
    #   "args": [
    #     "/home/<USER>/moralis-mcp"
    #   ],
    #   "env": {
    #     "MORALIS_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJub25jZSI6IjIwNzA5NThhLWU2ZGQtNDIwYi1iZWFhLTYzMmU5OTQ5MzY5YyIsIm9yZ0lkIjoiNDYzOTMwIiwidXNlcklkIjoiNDc3Mjg5IiwidHlwZUlkIjoiNzUwNDk4NzgtZWMxMi00ODNiLWE2NjYtMzM4ZTQ2MjBkZTE3IiwidHlwZSI6IlBST0pFQ1QiLCJpYXQiOjE3NTQ2MzcyOTMsImV4cCI6NDkxMDM5NzI5M30.65xerSvIorJ5akIk3lCu8qphNLUjK6I9V25T6_p_lQo"
    #   }
    # }
}