import asyncio
import json
from typing import List

from attr.validators import instance_of
from langchain_core.messages import BaseMessage, message_to_dict

from shovel_agent.constants import ChANNEL_VALUES, MESSAGES, CONTENT, ID, TS, USER_ID
from shovel_agent.graph import builder
from langgraph.checkpoint.memory import MemorySaver
from langgraph.store.memory import InMemoryStore
from langgraph.constants import (
    CONF,
    START,
)
from shovel_agent.constants import CONFIG_KEY_THREAD_ID,CONFIG_KEY_CHECKPOINT_ID

async def main(conversation: List[str]):
    # mem_store = InMemoryStore()
    checkpointer = MemorySaver()

    graph = builder.compile(checkpointer=checkpointer)
    # graph = builder.compile(store=mem_store, checkpointer=checkpointer)
    user_id = "test-user"
    config = {
        CONF: {},
        "user_id": user_id,
    }

    # for content in conversation:
    #     # 假设这里添加流式输出的逻辑，具体实现依赖于 graph.ainvoke 的流式支持
    #     async for result in graph.astream(
    #         {"messages": [{"role":"user", "content": content}]},
    #         {**config, "thread_id": "thread"},
    #         stream_mode="updates",
    #     ):
    #         # 处理流式结果
    #         print(result)
    #         pass

    # stream_mode = "messages"
    # async for result in graph.astream(
    #     {"messages": [{"role":"user", "content": conversation[0]}]},
    #     {**config, "thread_id": "thread"},
    #     stream_mode=stream_mode,
    # ):
    #     # 处理流式结果
    #     print(message_to_dict(result[0]) if stream_mode == "messages" else result)
    #     pass
    #

    result = await graph.ainvoke(
        {"messages": [{"role":"user", "content": conversation[0]}]},
        {**config, CONFIG_KEY_THREAD_ID: "thread", "user_id": user_id},
    )

    response = {
        "messages": [
            message_to_dict(msg)
            if isinstance(msg, BaseMessage) else msg
            for msg in result["messages"]
        ]
    }
    print(json.dumps(response))

    result = await graph.ainvoke(
        {"messages": [{"role":"user", "content": conversation[1]}]},
        {**config, CONFIG_KEY_THREAD_ID: "thread", "user_id": user_id},
    )

    response = {
        "messages": [
            message_to_dict(msg)
            if isinstance(msg, BaseMessage) else msg
            for msg in result["messages"]
        ]
    }
    print(json.dumps(response))

    state = graph.get_state({CONF:{CONFIG_KEY_THREAD_ID: "thread"}})
    if state  and state.metadata and state.metadata.get(USER_ID, '') == user_id:
        print( {
            "messages": [
                message_to_dict(msg)
                if isinstance(msg, BaseMessage) else msg
                for msg in state.values["messages"]
            ]
        })
    else:
        print ( {
            "messages": []
        })

    # The states are returned in reverse chronological order.
    query_config = None
    states = list(checkpointer.list(query_config, filter={"user_id": "test-user"}))
    # 如果states不为空，则遍历states
    if states:
        #初始化thread_list变量
        thread_list = []
        thread_id = states[0].config[CONF][CONFIG_KEY_THREAD_ID]
        ts = states[0].checkpoint[TS]
        thread_start_msg = ''
        for state in states:
            if thread_id != state.config[CONF][CONFIG_KEY_THREAD_ID]:
                thread_list.append({MESSAGES:thread_start_msg, CONFIG_KEY_THREAD_ID: thread_id, TS:ts})
                thread_start_msg = ''
                thread_id = state.config[CONF][CONFIG_KEY_THREAD_ID]
            elif START in state.checkpoint[ChANNEL_VALUES]:
                #当前只获取用户的输入的检查点
                thread_start_msg = state.checkpoint[ChANNEL_VALUES][START][MESSAGES][0][CONTENT]
                ts = state.checkpoint[TS]
                pass
                # result = {MESSAGES:state.checkpoint[CHANNEL_VALUES][START][MESSAGES][0][CONTENT],CONFIG_KEY_CHECKPOINT_ID: state.checkpoint[ID],CONFIG_KEY_THREAD_ID:state.config[CONF][CONFIG_KEY_THREAD_ID]}
                # print(result)
        thread_list.append({MESSAGES: thread_start_msg, CONFIG_KEY_THREAD_ID: thread_id, TS:ts})
        print(sorted(thread_list, key=lambda x: x.get(TS, ''), reverse=True))


    # for content in conversation:
    #      print(await graph.ainvoke(
    #         {"messages": [("user", content)]},
    #         {**config, "thread_id": "thread"},
    #     ))
    # namespace = ("memories", user_id)
    # memories = mem_store.search(namespace)
    #
    # # ls.expect(len(memories)).to_be_greater_than(0)
    # print(f"----------------: {memories}")
    #
    # bad_namespace = ("memories", "wrong-user")
    # bad_memories = mem_store.search(bad_namespace)
    # #ls.expect(len(bad_memories)).to_equal(0)
    # print(f"----------------length of bad_memories: {len(bad_memories)}")


if __name__ == "__main__":
    asyncio.run(main(["what's Pendle?","how to make yield on Pendle?"]))