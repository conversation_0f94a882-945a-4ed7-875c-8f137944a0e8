import json

from langchain_core.tools import tool
from langgraph.types import interrupt


@tool(return_direct=True, parse_docstring=True)
def stake_action(amount: float) -> str:
    """
    stake eth to lido protocol

    Args:
        amount (float): [REQUIRED] eth amount to be staked，ask user for input if absent

    Returns:
        str: raw transaction。

    """
    unsigned_tx = {"contract_address":"******************************************", "method":"stakeETH", "amount": amount}
    return interrupt(json.dumps(unsigned_tx))