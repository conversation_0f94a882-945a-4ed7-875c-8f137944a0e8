import json
from typing import Annotated

from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState
from langgraph.types import interrupt
from shovel_agent.service.dify_service import initialize_dify_service
from shovel_agent.utils import get_last_user_message

# 初始化Dify服务
dify_service = initialize_dify_service()


@tool(return_direct=True, parse_docstring=True)
def recommend_action(messages: Annotated[list, InjectedState("messages")]) -> str:
    """
    recommend product based on apy or tvl

    Args:
        messages (list): Inject at runtime, does not need to assign value。

    Returns:
        str: recommended product list。

    """
    try:
        # 获取最后一个人工输入的消息
        last_user_message = get_last_user_message(messages)
        # 调用Dify服务获取推荐数据
        result = dify_service.run_workflow(user_input=last_user_message, bear_token='app-1LXhZIUqjFtx5HjM7oEXzhGf')
        
        # 解析返回的数据
        if result and 'data' in result and 'outputs' in result['data'] and 'data' in result['data']['outputs']:
            product_list = result['data']['outputs']['data']
            
            # 添加contract_address字段（如果需要）
            for product in product_list:
                if 'contract_address' not in product:
                    product['contract_address'] = '0xa400C1faA8e2aA4447D5dBD63F32D6E9Be5f79ED'
        else:
            # 如果数据格式不符合预期，使用默认空列表
            product_list = []
    except Exception as e:
        # 处理异常
        print(f"Error calling Dify service: {str(e)}")
        product_list = []
    
    return interrupt(json.dumps({"action_type":"recommend","products":product_list})) if len(product_list) > 0 else "no product."
