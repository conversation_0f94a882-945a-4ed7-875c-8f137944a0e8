import json
from typing import Annotated

from langchain_core.tools import tool
from langgraph.prebuilt import InjectedState
from langgraph.types import interrupt
from shovel_agent.service.dify_service import initialize_dify_service
from shovel_agent.utils import get_last_user_message

# 初始化Dify服务
dify_service = initialize_dify_service()


@tool(return_direct=True, parse_docstring=True)
def market_data(messages: Annotated[list, InjectedState("messages")]) -> str:
    """
    query token price or token information or price ranking or price change percentage of one or more tokens

    Args:
        messages (list): Inject at runtime, does not need to assign value。

    Returns:
        str: market data of token。

    """
    try:
        # 获取最后一个人工输入的消息
        last_user_message = get_last_user_message(messages)

        # 调用Dify服务获取市场数据，使用指定的bear_token
        result = dify_service.run_workflow(
            user_input=last_user_message,
            bear_token='app-VqncYEPldimIqWXRv2XQHQUF'
        )
        # 解析返回的数据
        if result and 'data' in result and 'outputs' in result['data'] and 'data' in result['data']['outputs']:
            data = result['data']['outputs']['data']
        else:
            # 如果数据格式不符合预期，使用空数据
            data = []
    except Exception as e:
        # 处理异常
        print(f"Error calling Dify service: {str(e)}")
        data = []

    return json.dumps(data)
