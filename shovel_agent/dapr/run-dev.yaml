version: 1

apps:
  - appID: alphafi-agent
    appDirPath: ../
    appPort: 8000
    command: ["uv", "run", "src/shovel_agent/app.py"]
    env:
      SERVER_HOST: "0.0.0.0"
      SERVER_PORT: "8000"

      LOG_LEVEL: "info"
      
    # Dapr sidecar配置
    daprHTTPPort: 3600
    daprGRPCPort: 36000
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6190
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3