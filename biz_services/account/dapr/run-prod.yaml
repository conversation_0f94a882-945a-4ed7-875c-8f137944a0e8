version: 1

apps:
  - appID: biz_account
    appDirPath: ../
    appPort: 9053
    command: ["./bin/biz_account"]
    resourcesPath: "./dapr/components"

    env:
      SERVER_HOST: "0.0.0.0"
      SERVER_PORT: "9053"

      DAPR_SIDECAR_PORT: "3573"
      LOG_LEVEL: "debug"
      # JWT相关配置
      JWT_EXPIRATION_HOURS: "720"
      JWT_ISSUER: "alphafi"
      JWT_DAPR_SECRET_STORE: "secretstore"
      JWT_SECRET_KEY_NAME: "jwt-secret"
      
    # Dapr sidecar配置
    daprHTTPPort: 3573
    daprGRPCPort: 35073
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6163
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3