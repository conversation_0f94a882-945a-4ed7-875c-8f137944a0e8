package models

// LoginRequest 登录请求结构体
type LoginRequest struct {
	Signature string `json:"signature" binding:"required"` // 以太坊消息签名
	Message   string `json:"message" binding:"required"`   // 原始消息内容
	Account   string `json:"account"`                      // 以太坊地址（可选，用于验证）
}

// LoginResponse 登录响应结构体
type LoginResponse struct {
	Code    int       `json:"code"`           // 错误代码
	Success bool      `json:"success"`        // 是否成功
	Message string    `json:"message"`        // 响应消息
	Data    *UserData `json:"data,omitempty"` // 用户数据（成功时返回）
}

// User 用户信息结构体
type User struct {
	Address   string `json:"address"`   // 以太坊地址
	LoginTime int64  `json:"loginTime"` // 登录时间戳
}

// UserData 用户数据结构体（包含JWT token）
type UserData struct {
	User  *User  `json:"user"`  // 用户信息
	Token string `json:"token"` // JWT token
}

// ErrorResponse 错误响应结构体
type ErrorResponse struct {
	Success bool   `json:"success"` // 固定为false
	Message string `json:"message"` // 错误消息
	Code    int    `json:"code"`    // 错误代码
}

// ValidateTokenRequest JWT验证请求结构体
type ValidateTokenRequest struct {
	Token string `json:"token" binding:"required"` // JWT token
}

// ValidateTokenResponse JWT验证响应结构体
type ValidateTokenResponse struct {
	Code    int        `json:"code"`           // 错误代码
	Success bool       `json:"success"`        // 是否成功
	Message string     `json:"message"`        // 响应消息
	Data    *TokenInfo `json:"data,omitempty"` // Token信息（验证成功时返回）
}

// TokenInfo Token信息结构体
type TokenInfo struct {
	Address   string `json:"address"`   // 用户地址
	IssuedAt  int64  `json:"issuedAt"`  // 签发时间戳
	ExpiresAt int64  `json:"expiresAt"` // 过期时间戳
	Issuer    string `json:"issuer"`    // 签发者
	Valid     bool   `json:"valid"`     // 是否有效
}

// RefreshTokenRequest 刷新Token请求结构体
type RefreshTokenRequest struct {
	Token string `json:"token" binding:"required"` // 当前JWT token
}

// RefreshTokenResponse 刷新Token响应结构体
type RefreshTokenResponse struct {
	Code    int    `json:"code"`           // 错误代码
	Success bool   `json:"success"`        // 是否成功
	Message string `json:"message"`        // 响应消息
	Data    *Token `json:"data,omitempty"` // 新Token（刷新成功时返回）
}

// Token Token结构体
type Token struct {
	Token     string `json:"token"`     // JWT token
	ExpiresAt int64  `json:"expiresAt"` // 过期时间戳
}
