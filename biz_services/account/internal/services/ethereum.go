package services

import (
	"crypto/ecdsa"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/common/hexutil"
	"github.com/ethereum/go-ethereum/crypto"
)

// EthereumService 以太坊相关服务
type EthereumService struct{}

// NewEthereumService 创建新的以太坊服务实例
func NewEthereumService() *EthereumService {
	return &EthereumService{}
}

// VerifySignature 验证以太坊消息签名
func (s *EthereumService) VerifySignature(message, signature, expectedAddress string) error {
	// 1. 验证签名格式
	if err := s.validateSignatureFormat(signature); err != nil {
		return fmt.Errorf("签名格式无效: %w", err)
	}

	// 2. 验证地址格式（如果提供了地址）
	if expectedAddress != "" {
		if err := s.validateAddressFormat(expectedAddress); err != nil {
			return fmt.Errorf("地址格式无效: %w", err)
		}
	}

	// 3. 从签名中恢复公钥和地址
	recoveredAddress, err := s.recoverAddressFromSignature(message, signature)
	if err != nil {
		return fmt.Errorf("从签名恢复地址失败: %w", err)
	}

	// 4. 如果提供了期望地址，验证是否匹配
	if expectedAddress != "" {
		if !strings.EqualFold(recoveredAddress, expectedAddress) {
			return errors.New("签名地址与提供的地址不匹配")
		}
	}

	return nil
}

// validateSignatureFormat 验证签名格式
func (s *EthereumService) validateSignatureFormat(signature string) error {
	// 移除0x前缀
	signature = strings.TrimPrefix(signature, "0x")

	// 验证长度（65字节 = 130个十六进制字符）
	if len(signature) != 130 {
		return errors.New("签名长度必须为130个字符（65字节）")
	}

	// 验证是否为有效的十六进制字符串
	if _, err := hex.DecodeString(signature); err != nil {
		return errors.New("签名必须为有效的十六进制字符串")
	}

	return nil
}

// validateAddressFormat 验证以太坊地址格式
func (s *EthereumService) validateAddressFormat(address string) error {
	if !common.IsHexAddress(address) {
		return errors.New("无效的以太坊地址格式")
	}
	return nil
}

// recoverAddressFromSignature 从签名中恢复地址
func (s *EthereumService) recoverAddressFromSignature(message, signature string) (string, error) {
	// 创建以太坊消息哈希
	messageHash := s.createEthereumMessageHash(message)

	// 解码签名
	sigBytes, err := hexutil.Decode(signature)
	if err != nil {
		return "", fmt.Errorf("解码签名失败: %w", err)
	}

	// 调整v值（以太坊签名的v值需要减去27）
	if sigBytes[64] >= 27 {
		sigBytes[64] -= 27
	}

	// 恢复公钥
	pubKey, err := crypto.SigToPub(messageHash, sigBytes)
	if err != nil {
		return "", fmt.Errorf("恢复公钥失败: %w", err)
	}

	// 从公钥生成地址
	address := crypto.PubkeyToAddress(*pubKey)
	return address.Hex(), nil
}

// createEthereumMessageHash 创建以太坊消息哈希
func (s *EthereumService) createEthereumMessageHash(message string) []byte {
	// 以太坊消息前缀
	prefix := fmt.Sprintf("\x19Ethereum Signed Message:\n%d", len(message))
	fullMessage := prefix + message
	return crypto.Keccak256([]byte(fullMessage))
}

// GetAddressFromPublicKey 从公钥获取地址（辅助方法）
func (s *EthereumService) GetAddressFromPublicKey(pubKey *ecdsa.PublicKey) string {
	address := crypto.PubkeyToAddress(*pubKey)
	return address.Hex()
}

// RecoverAddressFromSignature 从签名中恢复地址（公共方法）
func (s *EthereumService) RecoverAddressFromSignature(message, signature string) (string, error) {
	return s.recoverAddressFromSignature(message, signature)
}
