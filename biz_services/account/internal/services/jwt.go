package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"shovel/biz_services/account/internal/config"
	"time"

	"github.com/golang-jwt/jwt/v5"
)

// JWTService JWT服务
type JWTService struct {
	config *config.JWTConfig
}

// NewJWTService 创建新的JWT服务实例
func NewJWTService(cfg *config.JWTConfig) *JWTService {
	return &JWTService{
		config: cfg,
	}
}

// Claims JWT声明结构体
type Claims struct {
	Address string `json:"address"` // 用户钱包地址
	jwt.RegisteredClaims
}

// TokenInfo Token信息结构体
type TokenInfo struct {
	Address   string    `json:"address"`   // 用户地址
	IssuedAt  time.Time `json:"issuedAt"`  // 签发时间
	ExpiresAt time.Time `json:"expiresAt"` // 过期时间
	Issuer    string    `json:"issuer"`    // 签发者
}

// GenerateToken 生成JWT token
func (s *JWTService) GenerateToken(userAddress string) (string, error) {
	if userAddress == "" {
		return "", errors.New("用户地址不能为空")
	}

	// 获取JWT密钥
	secretKey, err := s.getSecretKey()
	if err != nil {
		return "", fmt.Errorf("获取JWT密钥失败: %w", err)
	}

	// 设置过期时间
	expirationTime := time.Now().Add(time.Duration(s.config.ExpirationHours) * time.Hour)

	// 创建声明
	claims := &Claims{
		Address: userAddress,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expirationTime),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    s.config.Issuer,
			Subject:   userAddress,
		},
	}

	// 创建token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名token
	tokenString, err := token.SignedString([]byte(secretKey))
	if err != nil {
		return "", fmt.Errorf("签名token失败: %w", err)
	}

	return tokenString, nil
}

// ValidateToken 验证JWT token
func (s *JWTService) ValidateToken(tokenString string) (*TokenInfo, error) {
	if tokenString == "" {
		return nil, errors.New("token不能为空")
	}

	// 获取JWT密钥
	secretKey, err := s.getSecretKey()
	if err != nil {
		return nil, fmt.Errorf("获取JWT密钥失败: %w", err)
	}

	// 解析token
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(secretKey), nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, errors.New("token已过期")
		}
		if errors.Is(err, jwt.ErrTokenNotValidYet) {
			return nil, errors.New("token尚未生效")
		}
		if errors.Is(err, jwt.ErrTokenMalformed) {
			return nil, errors.New("token格式错误")
		}
		return nil, fmt.Errorf("token验证失败: %w", err)
	}

	// 检查token是否有效
	if !token.Valid {
		return nil, errors.New("token无效")
	}

	// 提取声明
	claims, ok := token.Claims.(*Claims)
	if !ok {
		return nil, errors.New("无法解析token声明")
	}

	// 构建token信息
	tokenInfo := &TokenInfo{
		Address:   claims.Address,
		IssuedAt:  claims.IssuedAt.Time,
		ExpiresAt: claims.ExpiresAt.Time,
		Issuer:    claims.Issuer,
	}

	return tokenInfo, nil
}

// RefreshToken 刷新JWT token
func (s *JWTService) RefreshToken(tokenString string) (string, error) {
	// 首先验证当前token
	tokenInfo, err := s.ValidateToken(tokenString)
	if err != nil {
		return "", fmt.Errorf("无法刷新无效的token: %w", err)
	}

	// 生成新的token
	newToken, err := s.GenerateToken(tokenInfo.Address)
	if err != nil {
		return "", fmt.Errorf("生成新token失败: %w", err)
	}

	return newToken, nil
}

// getSecretKey 获取JWT密钥
func (s *JWTService) getSecretKey() (string, error) {
	// 构建请求URL
	url := fmt.Sprintf("http://localhost:%d/v1.0/secrets/%s/%s",
		s.config.DaprSidecarPort, s.config.DaprSecretStore, s.config.SecretKeyName)

	// 创建HTTP请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("创建请求失败: %w", err)
	}

	// 发送请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("请求Dapr Secret Store失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("Dapr Secret Store返回错误状态: %d", resp.StatusCode)
	}

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析JSON响应
	var secretResponse map[string]string
	if err := json.Unmarshal(body, &secretResponse); err != nil {
		return "", fmt.Errorf("解析Secret Store响应失败: %w", err)
	}

	// 获取密钥值
	secretKey, exists := secretResponse[s.config.SecretKeyName]
	if !exists {
		return "", fmt.Errorf("在Secret Store中未找到密钥: %s", s.config.SecretKeyName)
	}

	if secretKey == "" {
		return "", errors.New("Secret Store中的密钥为空")
	}

	return secretKey, nil
}

// IsTokenExpired 检查token是否即将过期（在指定分钟内）
func (s *JWTService) IsTokenExpired(tokenString string, withinMinutes int) (bool, error) {
	tokenInfo, err := s.ValidateToken(tokenString)
	if err != nil {
		return true, err
	}

	// 检查是否在指定时间内过期
	expirationThreshold := time.Now().Add(time.Duration(withinMinutes) * time.Minute)
	return tokenInfo.ExpiresAt.Before(expirationThreshold), nil
}
