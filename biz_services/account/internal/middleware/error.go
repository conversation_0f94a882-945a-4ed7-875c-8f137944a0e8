package middleware

import (
	"fmt"
	"log"
	"net/http"
	"shovel/biz_services/account/internal/models"

	"github.com/gin-gonic/gin"
)

// ErrorHandler 错误处理中间件
func ErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 检查是否有错误
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			// 记录错误日志
			log.Printf("请求错误: %s %s - %v", c.Request.Method, c.Request.URL.Path, err.Error())

			// 如果响应还没有写入，发送错误响应
			if !c.Writer.Written() {
				response := models.ErrorResponse{
					Success: false,
					Message: "服务器内部错误",
					Code:    http.StatusInternalServerError,
				}

				// 在开发模式下显示详细错误信息
				if gin.Mode() == gin.DebugMode {
					response.Message = err.Error()
				}

				c.<PERSON>(http.StatusInternalServerError, response)
			}
		}
	}
}

// CORS 跨域处理中间件
func CORS() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// RequestLogger 请求日志中间件
func RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("[%s] %s %s %d %s %s\n",
			param.TimeStamp.Format("2006-01-02 15:04:05"),
			param.Method,
			param.Path,
			param.StatusCode,
			param.Latency,
			param.ClientIP,
		)
	})
}

// Recovery 恢复中间件，处理panic
func Recovery() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		log.Printf("Panic恢复: %v", recovered)

		response := models.ErrorResponse{
			Success: false,
			Message: "服务器内部错误",
			Code:    http.StatusInternalServerError,
		}

		c.JSON(http.StatusInternalServerError, response)
	})
}
