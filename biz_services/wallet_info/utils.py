def security_score_to_label(security_score):
    risk_score = 100 - security_score
    label = ""
    risk_multiplier = 0
    if risk_score <= 15:
        label = "BlueChip"
        risk_multiplier = 1
    elif risk_score <= 45:
        label = "LargeCap"
        risk_multiplier = 5
    elif risk_score <= 75:
        label = "SmallCap"
        risk_multiplier = 10
    elif risk_score <= 100:
        label = "MicroCap"
        risk_multiplier = 100
    return label, risk_multiplier