import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware
import aiohttp
from holdings_service import * 
from utils import *
from txs_service import * 



sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
app = FastAPI()


@app.get("/alphafi/wallet/holdings", summary="获取持仓列表", description="获取持仓列表[价值小于1美金的仓位忽略]", tags=["钱包相关"])
async def get_holdings(address: str, chain_name: str) -> dict:
        return await WalletHoldingsService.get_holdings(address=address, chain_name=chain_name)

@app.get("/alphafi/wallet/risk_rating_by_holdings", summary="获取风险评定[根据持仓情况]", description="根据持仓情况，获取风险评定", tags=["钱包相关"])
def risk_rating_by_holdings(address, chain_name):
    """
    风险评级-根据持仓情况
    Args:
        address: 钱包地址
    """
    result = asyncio.run(get_holdings(address, chain_name))
    if result["code"] == 200:
        holding_list = result["data"]
        # 若持仓为0
        if len(holding_list) == 0:
            return {"code":200,
                    "message":"success",
                    "data":{"rating_role": "Beginner",
                            "risk_level": 0,
                            "risk_score": 0}}
        total_usd_value = 0
        risk_value = 0
        print(holding_list)
        for holding in holding_list:
            if holding["security_score"] is None:
                continue
            label, risk_multiplier = security_score_to_label(holding["security_score"])
            risk_value += risk_multiplier * holding["usd_value"]
            total_usd_value += holding["usd_value"]
        risk_value /= total_usd_value
        print(risk_value)
        if risk_value <= 1:
            risk_level = 1
            risk_score = risk_value * 15
            rating_role = "Robust"
        elif risk_value <= 5:
            risk_level = 2
            risk_score = 15 + 7.5 * (risk_value - 1)
            rating_role = "Low-Risk"
        elif risk_value <= 10:
            risk_level = 3
            risk_score = 45 + 6 * (risk_value - 5)
            rating_role = "Moderate-Risk"
        elif risk_value <= 50:
            risk_level = 4
            risk_score = 75 + 0.375 * (risk_value - 10)
            rating_role = "High-Risk"
        else:
            risk_level = 5
            risk_score = 90 + 0.2 * (risk_value - 50)
            rating_role = "Extreme-Risk"

        return {"code":200,
                "message":"success",
                "data":{"rating_role": rating_role,
                        "risk_level": risk_level,
                        "risk_score": round(risk_score)}}
    else:
        return result
    
@app.get("/alphafi/wallet/risk_rating_by_txs", summary="获取风险评定[根据交易情况]", description="根据交易情况，获取风险评定", tags=["钱包相关"])
def risk_rating_by_txs_count(address, chain_name):
    """
    风险评级-根据交易频次
    Args:
        address: 钱包地址
    """
    # 过去30天的交易次数
    result = asyncio.run(WalletTxsService.get_tx_counts_by_time(address, chain_name, 30 * 24))
    if result["code"] == 200:
        txs_count = result["data"]["tx_counts"]
        rating_role = "Unknown"
        risk_level = 0
        risk_score = 0
        if txs_count <= 5:
            rating_role = "Holder"
            risk_level = 1
            risk_score = txs_count * 3
        elif txs_count <= 20:
            rating_role = "Investor"
            risk_level = 2
            risk_score = (txs_count - 5)*2 + 15
        elif txs_count <= 50:
            rating_role = "Active"
            risk_level = 3
            risk_score = (txs_count - 20)*1 + 45
        elif txs_count <= 100:
            rating_role = "Frequent"
            risk_level = 4
            risk_score = (txs_count-50)*0.3 + 75
        else:
            rating_role = "High-Freq"
            risk_level = 5
            risk_score = round(10 * (txs_count - 100) / txs_count + 90)
        return {
            "code": result["code"],
            "message":"success",
            "data":{
                "rating_role": rating_role,
                "risk_level": risk_level,
                "risk_score": risk_score
            }
        }
    else:
        return result


@app.get("/alphafi/wallet/risk_rating_general", summary="获取整体风险评定", description="根据钱包整体情况，获取风险评定", tags=["钱包相关"])
def risk_rating_general(address, chain_name):
    """
    风险评级-钱包整体情况
    Args:
        address: 钱包地址
    """
    rating_by_holdings = risk_rating_by_holdings(address, chain_name)
    # 若持仓为0
    if rating_by_holdings["data"]["rating_role"] == "Beginner":
        return {"code":200,
                "message": "success",
                "data":{"rating_role_general": rating_by_holdings["data"]["rating_role"],
                        "risk_level_general": rating_by_holdings["data"]["risk_level"],
                        "risk_score_general": rating_by_holdings["data"]["risk_score"]}}
    risk_score_by_holdings = rating_by_holdings["data"]["risk_score"]
    rating_by_txs = risk_rating_by_txs_count(address, chain_name)
    risk_score_by_txs = rating_by_txs["data"]["risk_score"]

    general_score = risk_score_by_holdings * 0.7 + risk_score_by_txs * 0.3
    if general_score <= 15:
        rating_role = "Robust"
        rating_level = 1
    elif general_score <= 45:
        rating_role = "Low-Risk"
        rating_level = 2
    elif general_score <= 75:
        rating_role = "Moderate-Risk"
        rating_level = 3
    elif general_score <= 90:
        rating_role = "High-Risk"
        rating_level = 4
    else:
        rating_role = "Extreme-Risk"
        rating_level = 5
    return {"code":200,
            "message":"success",
            "data":{"rating_role_general": rating_role,
                    "risk_level_general": rating_level,
                    "risk_score_general": round(general_score)}}



if __name__ == "__main__":
    
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )