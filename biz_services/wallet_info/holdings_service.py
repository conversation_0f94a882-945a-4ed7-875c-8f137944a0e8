import os
import aiohttp

class WalletHoldingsService:
    @staticmethod
    async def get_holdings(address: str, chain_name: str) -> dict:
        """
        获取钱包持仓代币列表

        Args:
            address: 钱包地址
        """
        url = f"https://deep-index.moralis.io/api/v2.2/wallets/{address}/tokens?chain={chain_name}"
        headers = {
            "accept": "application/json",
            "X-API-Key": os.getenv('MORALIS_API_KEY', "")
        }
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers) as resp:
                    if resp.status != 200:
                        raise Exception(f"Moralis API Error: {resp.status}")
                    data = await resp.json()
                    tokens = []
                    for item in data["result"]:
                        keys_to_keep = ['symbol', 'name', "balance_formatted", "usd_price", "security_score", "usd_value",
                                    "portfolio_percentage", "token_address", "native_token", "logo"]
                        new_dict = {k: item[k] for k in keys_to_keep if k in item}
                        # 价值小于1美金的仓位忽略
                        if not new_dict["usd_value"] < 1:
                            tokens.append(new_dict)

                    return {"code": 200, "message":"success", "data": tokens}
        except Exception as e:
            return {
                "code": 5000,
                "message": str(e),
                "data": None
            }