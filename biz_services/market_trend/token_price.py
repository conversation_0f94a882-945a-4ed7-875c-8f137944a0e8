
from typing import List, Dict, Any
import database

class TokenPriceService:
    @staticmethod
    async def query_token_price(
        symbol: str = None,
        sort_column: str = "price_change_percentage_24h",
        sort_order: str = "desc",
        limit: int = 10
    ) -> List[Dict[str, Any]]:
   
        # 构建SQL查询
        query_parts = [
            "SELECT symbol, price_change_percentage_24h, name, current_price, ath, atl",
            "FROM coingecko_market_price",
            "WHERE price_change_percentage_24h IS NOT NULL"
        ]
        params = []

        # 如果symbol不为空，则添加到查询条件
        if symbol:
            query_parts.append("  AND LOWER(symbol) = %s")
            params.append(symbol.lower())

        # 添加排序和限制
        query_parts.append(f"ORDER BY {sort_column} {sort_order}")
        query_parts.append("LIMIT %s")
        params.append(limit)

        # 组合查询语句
        query = "\n".join(query_parts)

        # 执行查询
        params = tuple(params)
        db_service = await database.DatabaseService.get_instance()
        result = await db_service.execute_query(query, params)

        return result