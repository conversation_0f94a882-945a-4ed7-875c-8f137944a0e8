import os
import sys
import asyncio
import uvicorn
from fastapi import FastAPI, Query
from fastapi.middleware.cors import CORSMiddleware

sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'commons'))
import database
import token_price

app = FastAPI()

@app.get("/alphafi/agent/query/token/price", summary="查询代币价格信息接口", description="查询price_change_percentage_24h、name、current_price、ath、atl、create_time信息，可选择按symbol过滤")
async def query_token_price(
    symbol: str = Query(None, description="代币符号，可选"),
    sort_column: str = Query("price_change_percentage_24h", description="排序字段，可选值: price_change_percentage_24h, current_price, ath, atl"),
    sort_order: str = Query("desc", description="排序顺序，可选值: asc(升序), desc(降序)"),
    limit: int = Query(5, description="返回数据数量限制", ge=1)
):
    """查询代币价格信息接口

    Args:
        symbol (str, optional): 代币符号，默认为None
        sort_column (str): 排序字段，默认为 'price_change_percentage_24h'
        sort_order (str): 排序顺序，默认为 'desc'
        limit (int): 返回数据数量限制，默认为 10

    Returns:
        dict: 包含代币价格信息的响应
    """
    # 验证排序字段
    valid_sort_fields = ['price_change_percentage_24h', 'current_price', 'ath', 'atl']
    if sort_column not in valid_sort_fields:
        sort_column = 'price_change_percentage_24h'

    # 验证排序顺序
    valid_sort_orders = ['asc', 'desc']
    if sort_order.lower() not in valid_sort_orders:
        sort_order = 'desc'

    # 调用服务层获取数据
    result = await token_price.TokenPriceService.query_token_price(
        symbol=symbol,
        sort_column=sort_column,
        sort_order=sort_order,
        limit=limit
    )

    return {
        "code": 200,
        "message": "success",
        "data": result
    }


async def init():
    # 初始化数据库连接
    await database.initialize_database()

if __name__ == "__main__":
    
    asyncio.run(init())
    app.add_middleware(
        CORSMiddleware,
        allow_origins=['*'],
        allow_credentials=True,
        allow_methods=['*'],
        allow_headers=['*'],
    )

    port = int(os.getenv('SERVER_PORT', 8000))
    uvicorn.run(
        app,
        host=os.getenv('SERVER_HOST', "0.0.0.0"),
        port=port,
        log_config={
            "version": 1,
            "formatters": {
                "default": {
                    "()": "uvicorn.logging.DefaultFormatter",
                    "fmt": "%(asctime)s - %(levelname)s - %(message)s",
                    "datefmt": "%Y-%m-%d %H:%M:%S"
                }
            },
            "handlers": {
                "default": {
                    "formatter": "default",
                    "class": "logging.StreamHandler",
                    "stream": "ext://sys.stderr"
                }
            },
            "loggers": {
                "uvicorn": {"handlers": ["default"], "level": "INFO"}
            }
        }
    )