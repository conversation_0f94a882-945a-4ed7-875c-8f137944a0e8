import database
from datetime import datetime

class OnChainService:
    _instance = None
    _db_service = None
    
    @classmethod
    async def get_instance(cls):
        if cls._instance is None:
            cls._instance = cls()
            # 初始化数据库服务
            cls._db_service = await database.DatabaseService.get_instance()
        return cls._instance
    
    async def insert_operation(self, thread_id: str, user_id: str, operation_type: str = None, 
                              json_params: str = None, status: int = 0):
        # 创建数据字典
        data = {
            'thread_id': thread_id,
            'user_id': user_id,
            'operation_type': operation_type,
            'json_params': json_params,
            'status': status,
            'create_time': datetime.now(),
            'update_time': datetime.now()
        }
        
        # 执行插入
        result = await self._db_service.insert('on_chain_operation', data)
        
        # 返回插入记录的ID
        return result['id']
    
    async def update_operation(self, id: int, user_id: str, status: int, tx_id: str = None):
        # 创建更新数据字典
        data = {
            'status': status,
            'tx_id': tx_id,
            'update_time': datetime.now()
        }
        
        # 构建WHERE子句
        where_clause = 'id = %s AND user_id = %s'
        where_params = (id, user_id)
        
        # 执行更新
        result = await self._db_service.update('on_chain_operation', data, where_clause, where_params)
        
        return result
    
    async def query_operations(self, thread_id: str = None, user_id: str = None, 
                              status: int = None, limit: int = 100):
        # 构建SQL查询
        query_parts = [
            "SELECT id, thread_id, user_id, operation_type, json_params, status, tx_id, create_time, update_time",
            "FROM on_chain_operation"
        ]
        
        # 添加过滤条件
        params = []
        has_where = False
        
        if thread_id is not None:
            query_parts.append("WHERE thread_id = %s")
            params.append(thread_id)
            has_where = True
        
        if user_id is not None:
            if has_where:
                query_parts.append("AND user_id = %s")
            else:
                query_parts.append("WHERE user_id = %s")
                has_where = True
            params.append(user_id)
        
        if status is not None:
            if has_where:
                query_parts.append("AND status = %s")
            else:
                query_parts.append("WHERE status = %s")
                has_where = True
            params.append(status)
        
        # 添加排序和限制
        query_parts.append("ORDER BY create_time DESC")
        query_parts.append("LIMIT %s")
        params.append(limit)
        
        # 组合查询语句
        query = "\n".join(query_parts)
        
        # 执行查询
        result = await self._db_service.execute_query(query, tuple(params))
        
        return result
    
    async def query_operation_by_id(self, id: int):
        # 构建SQL查询
        query = """
            SELECT id, thread_id, user_id, operation_type, json_params, status, tx_id, create_time, update_time
            FROM on_chain_operation
            WHERE id = %s
        """
        
        # 执行查询
        result = await self._db_service.execute_query(query, (id,))
        
        # 返回单条记录或None
        return result[0] if result else None