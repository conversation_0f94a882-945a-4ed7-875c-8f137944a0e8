import database

class DefiPoolService:
    @staticmethod
    async def query_pools(sort_by: str = 'apy', sort_order: str = 'desc', limit: int = 10, 
                         apy_low: float = None, apy_high: float = None, 
                         tvlUsd_low: float = None, tvlUsd_high: float = None):
        # 验证排序字段
        valid_sort_fields = ['apy', 'tvlUsd', 'symbol', 'project']
        if sort_by not in valid_sort_fields:
            sort_by = 'apy'

        # 验证排序顺序
        sort_order = 'DESC' if sort_order.lower() == 'desc' else 'ASC'

        # 构建SQL查询
        query_parts = [
            "SELECT symbol, project, apy, tvlUsd",
            "FROM defi_pools WHERE chain='Ethereum'"
        ]

        # 添加过滤条件
        params = []
        if apy_low is not None:
            query_parts.append("AND apy >= %s")
            params.append(apy_low)
        if apy_high is not None:
            query_parts.append("AND apy <= %s")
            params.append(apy_high)
        if tvlUsd_low is not None:
            query_parts.append("AND tvlUsd >= %s")
            params.append(tvlUsd_low)
        if tvlUsd_high is not None:
            query_parts.append("AND tvlUsd <= %s")
            params.append(tvlUsd_high)

        # 添加排序和限制
        query_parts.append(f"ORDER BY {sort_by} {sort_order}")
        query_parts.append("LIMIT %s")
        params.append(limit)

        # 组合查询语句
        query = "\n".join(query_parts)

        # 执行查询
        db_service = await database.DatabaseService.get_instance()
        result = await db_service.execute_query(query, tuple(params))

        return result
    

    @staticmethod
    async def query_pools_v2(project: str = None, symbol: str = None,
                             symbol_fuzzy: bool = False,
                             apy_low: float = None, apy_high: float = None, 
                             tvlUsd_low: float = None, tvlUsd_high: float = None):
        
        limit = 3
        sort_order = "DESC"
        sort_by = "tvlUsd"
        # 验证排序字段
        # valid_sort_fields = ['apy', 'tvlUsd', 'symbol', 'project']
        # if sort_by not in valid_sort_fields:
        #     sort_by = 'apy'


        # 构建SQL查询
        query_parts = [
            "SELECT symbol, project, apy, tvlUsd",
            "FROM defi_pools WHERE chain='Ethereum'"
        ]

        # 添加过滤条件
        params = []
        if project is not None:
            project = project.lower()
            query_parts.append("AND project LIKE %s")
            params.append(project + "%")
        if symbol is not None:
            symbol = symbol.upper()
            if symbol_fuzzy:
                query_parts.append("AND symbol LIKE %s")
                params.append("%" + symbol + "%")
            else:
                query_parts.append("AND symbol = %s")
                params.append(symbol)
        if apy_low is not None:
            query_parts.append("AND apy >= %s")
            params.append(apy_low)
        if apy_high is not None:
            query_parts.append("AND apy <= %s")
            params.append(apy_high)
        if tvlUsd_low is not None:
            query_parts.append("AND tvlUsd >= %s")
            params.append(tvlUsd_low)
        if tvlUsd_high is not None:
            query_parts.append("AND tvlUsd <= %s")
            params.append(tvlUsd_high)

        # 添加排序和限制
        query_parts.append(f"ORDER BY {sort_by} {sort_order}")
        query_parts.append("LIMIT %s")
        params.append(limit)

        # 组合查询语句
        query = "\n".join(query_parts)

        # 执行查询
        db_service = await database.DatabaseService.get_instance()
        result = await db_service.execute_query(query, tuple(params))

        return result