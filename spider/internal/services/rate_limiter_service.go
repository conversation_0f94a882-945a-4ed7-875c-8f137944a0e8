package services

import (
	"context"
	"fmt"
	"log"
	"shovel/spider/internal/models"
)

// RateLimiterService 全局限流服务
type RateLimiterService struct {
	redisClient *RedisClientService
}

// NewRateLimiterService 创建全局限流服务
func NewRateLimiterService() (*RateLimiterService, error) {
	redisClient, err := NewRedisClientService()
	if err != nil {
		return nil, fmt.Errorf("创建Redis客户端失败: %w", err)
	}

	return &RateLimiterService{
		redisClient: redisClient,
	}, nil
}

// AcquireResource 获取并发资源
func (s *RateLimiterService) AcquireResource(ctx context.Context, req *models.GlobalRateLimiterRequest) (*models.GlobalRateLimiterResponse, error) {
	log.Printf("尝试获取全局限流资源: limiter_id=%s, token=%s, max_concurrency=%d, max_qps=%.2f",
		req.<PERSON>iter<PERSON>, req.Token, req.MaxConcurrency, req.MaxQPS)

	// 调用Redis客户端执行Lua脚本（支持QPS限制）
	allowed, currentConcurrency, currentTokens, message, err := s.redisClient.AcquireResource(
		ctx, req.LimiterID, req.Token, req.MaxConcurrency, req.TimeoutSeconds, req.MaxQPS)
	if err != nil {
		log.Printf("Redis获取资源失败: %v", err)
		return &models.GlobalRateLimiterResponse{
			Success: false,
			Message: "获取资源失败",
			Error:   err.Error(),
		}, nil
	}

	log.Printf("Redis获取资源结果: allowed=%t, current_concurrency=%d, current_tokens=%d, message=%s",
		allowed, currentConcurrency, currentTokens, message)

	response := &models.GlobalRateLimiterResponse{
		Success:            true,
		Message:            message,
		Allowed:            allowed,
		CurrentConcurrency: currentConcurrency,
		CurrentTokens:      currentTokens,
	}

	if !allowed {
		response.WaitTime = 5 // 建议等待5秒
		if req.MaxQPS > 0 && currentTokens < 1 {
			// QPS限制导致的等待，建议更短的等待时间
			response.WaitTime = 1
		}
	}

	if allowed {
		log.Printf("成功获取全局限流资源: limiter_id=%s, token=%s", req.LimiterID, req.Token)
	} else {
		log.Printf("获取全局限流资源被拒绝: limiter_id=%s, token=%s, reason=%s", req.LimiterID, req.Token, message)
	}

	return response, nil
}

// ReleaseResource 释放并发资源
func (s *RateLimiterService) ReleaseResource(ctx context.Context, req *models.ReleaseRateLimiterRequest) (*models.GlobalRateLimiterResponse, error) {
	log.Printf("释放全局限流资源: limiter_id=%s, token=%s", req.LimiterID, req.Token)

	// 调用Redis客户端执行Lua脚本
	success, remainingCount, message, err := s.redisClient.ReleaseResource(ctx, req.LimiterID, req.Token)
	if err != nil {
		log.Printf("Redis释放资源失败: %v", err)
		return &models.GlobalRateLimiterResponse{
			Success: false,
			Message: "释放资源失败",
			Error:   err.Error(),
		}, nil
	}

	log.Printf("Redis释放资源结果: success=%t, remaining_count=%d, message=%s", success, remainingCount, message)

	response := &models.GlobalRateLimiterResponse{
		Success: success,
		Message: message,
	}

	if success {
		log.Printf("成功释放全局限流资源: limiter_id=%s, token=%s, remaining_count=%d", req.LimiterID, req.Token, remainingCount)
	}

	return response, nil
}

// Close 关闭服务
func (s *RateLimiterService) Close() error {
	if s.redisClient != nil {
		return s.redisClient.Close()
	}
	return nil
}
