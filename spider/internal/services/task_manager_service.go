package services

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"shovel/spider/config"
	"shovel/spider/internal/models"

	"github.com/dapr/go-sdk/client"
	"github.com/google/uuid"
)

// TaskService 定义任务服务接口
type TaskService interface {
	CreateTask(ctx context.Context, req *models.CreateTaskRequest) (*models.CrawlerTask, error)
	GetTask(ctx context.Context, taskID string) (*models.CrawlerTask, error)
	UpdateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus) error
	UpdateTask(ctx context.Context, taskID string, updates map[string]interface{}) error
	ListTasks(ctx context.Context, limit, offset int, status models.TaskStatus) ([]*models.CrawlerTask, error)
	DeleteTask(ctx context.Context, taskID string) error
	GetTaskCount(ctx context.Context, status models.TaskStatus) (int, error)
	Close() error
}

// taskServiceImpl 任务服务实现
type taskServiceImpl struct {
	daprClient      client.Client
	urlQueueService URLQueueService
	config          *config.Config
}

// NewTaskService 创建新的任务服务实例
func NewTaskService(cfg *config.Config) (TaskService, error) {
	// 创建Dapr客户端
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	// 创建URL队列服务
	urlQueueService, err := NewURLQueueService(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建URL队列服务失败: %w", err)
	}

	return &taskServiceImpl{
		daprClient:      daprClient,
		urlQueueService: urlQueueService,
		config:          cfg,
	}, nil
}

// CreateTask 创建新的爬虫任务
func (s *taskServiceImpl) CreateTask(ctx context.Context, req *models.CreateTaskRequest) (*models.CrawlerTask, error) {
	// 生成任务ID
	taskID := fmt.Sprintf("%x", uuid.New().ID())

	// 如果全局限流器ID为空，则使用taskID作为全局限流器ID
	globalRateLimiterID := req.GlobalRateLimiterID
	if globalRateLimiterID == "" {
		globalRateLimiterID = taskID
	}

	// 创建任务对象
	task := &models.CrawlerTask{
		ID:                  taskID,
		Name:                req.Name,
		Description:         req.Description,
		InitialURLs:         req.InitialURLs,
		Priority:            req.Priority,
		SpiderName:          req.SpiderName,
		MaxConcurrency:      req.MaxConcurrency,
		MaxQPS:              req.MaxQPS,
		Timeout:             req.Timeout,
		GlobalRateLimiterID: globalRateLimiterID,
		Status:              models.StatusPending,
		IsRecurring:         req.IsRecurring,
		RepeatInterval:      req.RepeatInterval,
		DownloadConfig:      req.DownloadConfig,
		ParseConfig:         req.ParseConfig,
		CreatedAt:           time.Now(),
		UpdatedAt:           time.Now(),
	}

	// 如果是周期性任务，设置LastExecutedAt为当前时间
	if task.IsRecurring {
		now := time.Now()
		task.LastExecutedAt = &now
	}

	// 1. 保存任务到Dapr状态存储
	taskKey := fmt.Sprintf("task:%s", taskID)
	taskData, err := json.Marshal(task)
	if err != nil {
		return nil, fmt.Errorf("序列化任务数据失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskKey, taskData, nil)
	if err != nil {
		return nil, fmt.Errorf("保存任务到状态存储失败: %w", err)
	}

	// 保存任务ID到任务列表（用于ListTasks）
	taskListKey := "task:list"
	existingList, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	var taskIDs []string
	if existingList.Value != nil {
		err = json.Unmarshal(existingList.Value, &taskIDs)
		if err != nil {
			return nil, fmt.Errorf("解析任务列表失败: %w", err)
		}
	}

	// 添加新任务ID到列表
	taskIDs = append(taskIDs, taskID)
	taskListData, err := json.Marshal(taskIDs)
	if err != nil {
		return nil, fmt.Errorf("序列化任务列表失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskListKey, taskListData, nil)
	if err != nil {
		return nil, fmt.Errorf("保存任务列表失败: %w", err)
	}

	// 2. 将InitialURLs添加到URL队列
	err = s.urlQueueService.AddURLs(ctx, taskID, req.InitialURLs, req.Priority)
	if err != nil {
		// 如果添加URL失败，需要清理已创建的任务
		if deleteErr := s.daprClient.DeleteState(ctx, s.config.Dapr.StateStore, taskKey, nil); deleteErr != nil {
			log.Printf("清理任务失败: %v", deleteErr)
		}

		// 从任务列表中移除任务ID
		result, listErr := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
		if listErr == nil && result.Value != nil {
			var taskIDs []string
			if jsonErr := json.Unmarshal(result.Value, &taskIDs); jsonErr == nil {
				updatedTaskIDs := make([]string, 0, len(taskIDs))
				for _, id := range taskIDs {
					if id != taskID {
						updatedTaskIDs = append(updatedTaskIDs, id)
					}
				}

				taskListData, jsonErr := json.Marshal(updatedTaskIDs)
				if jsonErr == nil {
					if saveErr := s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskListKey, taskListData, nil); saveErr != nil {
						log.Printf("清理任务时更新任务列表失败: %v", saveErr)
					}
				}
			}
		}

		return nil, fmt.Errorf("添加URL到队列失败: %w", err)
	}

	// 3. 发布任务创建事件到Dapr pub/sub
	taskEvent := &models.TaskCreatedEvent{
		TaskID:     taskID,
		TaskName:   req.Name,
		Priority:   req.Priority,
		SpiderName: req.SpiderName,
		URLCount:   len(req.InitialURLs),
		CreatedAt:  time.Now(),
	}

	eventData, err := json.Marshal(taskEvent)
	if err != nil {
		log.Printf("序列化任务创建事件失败: %v", err)
	} else {
		err = s.daprClient.PublishEvent(ctx, s.config.Dapr.PubSubName, "task.created", eventData)
		if err != nil {
			log.Printf("发布任务创建事件失败: %v", err)
		}
	}

	log.Printf("成功创建任务并保存到状态存储: %s，添加了 %d 个URL到队列", taskID, len(req.InitialURLs))
	return task, nil
}

// GetTask 根据任务ID获取任务信息
func (s *taskServiceImpl) GetTask(ctx context.Context, taskID string) (*models.CrawlerTask, error) {
	taskKey := fmt.Sprintf("task:%s", taskID)

	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskKey, nil)
	if err != nil {
		return nil, fmt.Errorf("从状态存储获取任务失败: %w", err)
	}

	if result.Value == nil {
		return nil, fmt.Errorf("任务不存在: %s", taskID)
	}

	var task models.CrawlerTask
	err = json.Unmarshal(result.Value, &task)
	if err != nil {
		return nil, fmt.Errorf("解析任务数据失败: %w", err)
	}

	return &task, nil
}

// UpdateTaskStatus 更新任务状态
func (s *taskServiceImpl) UpdateTaskStatus(ctx context.Context, taskID string, status models.TaskStatus) error {
	// 首先获取现有任务
	task, err := s.GetTask(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取任务失败: %w", err)
	}

	// 更新任务状态和更新时间
	task.Status = status
	task.UpdatedAt = time.Now()

	// 保存更新后的任务到状态存储
	taskKey := fmt.Sprintf("task:%s", taskID)
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskKey, taskData, nil)
	if err != nil {
		return fmt.Errorf("保存任务状态失败: %w", err)
	}

	// 发布任务状态变更事件
	taskStatusEvent := map[string]interface{}{
		"task_id":     taskID,
		"task_name":   task.Name,
		"spider_name": task.SpiderName,
		"status":      status,
		"updated_at":  time.Now(),
	}
	eventData, err := json.Marshal(taskStatusEvent)
	if err != nil {
		log.Printf("序列化任务状态变更事件失败: %v", err)
	} else {
		err = s.daprClient.PublishEvent(ctx, s.config.Dapr.PubSubName, "task.status.changed", eventData)
		if err != nil {
			log.Printf("发布任务状态变更事件失败: %v", err)
		}
	}

	log.Printf("成功更新任务状态: ID=%s Status=%s", taskID, status)
	return nil
}

// UpdateTask 更新任务信息
func (s *taskServiceImpl) UpdateTask(ctx context.Context, taskID string, updates map[string]interface{}) error {
	// 首先获取现有任务
	task, err := s.GetTask(ctx, taskID)
	if err != nil {
		return fmt.Errorf("获取任务失败: %w", err)
	}

	// 应用更新
	updated := false

	// 处理LastExecutedAt字段
	if lastExecutedAt, ok := updates["last_executed_at"]; ok {
		if timeValue, ok := lastExecutedAt.(time.Time); ok {
			task.LastExecutedAt = &timeValue
			updated = true
		}
	}

	// 处理DownloadConfig字段
	if downloadConfig, ok := updates["download_config"]; ok {
		if configValue, ok := downloadConfig.(string); ok {
			task.DownloadConfig = configValue
			updated = true
		}
	}

	// 处理ParseConfig字段
	if parseConfig, ok := updates["parse_config"]; ok {
		if configValue, ok := parseConfig.(string); ok {
			task.ParseConfig = configValue
			updated = true
		}
	}

	// TODO: 添加其他字段的更新逻辑

	// 如果没有任何字段被更新，直接返回
	if !updated {
		return nil
	}

	// 更新更新时间
	task.UpdatedAt = time.Now()

	// 保存更新后的任务到状态存储
	taskKey := fmt.Sprintf("task:%s", taskID)
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务数据失败: %w", err)
	}

	err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskKey, taskData, nil)
	if err != nil {
		return fmt.Errorf("保存任务信息失败: %w", err)
	}

	log.Printf("成功更新任务信息: %s\n", taskID)

	return nil
}

// ListTasks 获取任务列表
func (s *taskServiceImpl) ListTasks(ctx context.Context, limit, offset int, status models.TaskStatus) ([]*models.CrawlerTask, error) {
	// 获取任务ID列表
	taskListKey := "task:list"
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return nil, fmt.Errorf("获取任务列表失败: %w", err)
	}

	if result.Value == nil {
		return []*models.CrawlerTask{}, nil
	}

	var taskIDs []string
	err = json.Unmarshal(result.Value, &taskIDs)
	if err != nil {
		return nil, fmt.Errorf("解析任务列表失败: %w", err)
	}

	// 获取每个任务的详细信息并过滤
	filteredTasks := make([]*models.CrawlerTask, 0)

	// 如果不需要过滤状态，则设置计数器以便进行分页
	skipCount := 0
	count := 0

	for _, taskID := range taskIDs {
		// 获取任务详情
		task, err := s.GetTask(ctx, taskID)
		if err != nil {
			// 如果某个任务获取失败，记录错误但继续处理其他任务
			log.Printf("获取任务 %s 失败: %v\n", taskID, err)
			continue
		}

		// 如果指定了状态过滤，且任务状态不匹配，则跳过
		if status != "" && task.Status != status {
			continue
		}

		// 处理分页
		skipCount++
		if skipCount <= offset {
			continue
		}

		filteredTasks = append(filteredTasks, task)
		count++

		// 达到限制数量后停止
		if limit > 0 && count >= limit {
			break
		}
	}

	return filteredTasks, nil
}

// DeleteTask 删除任务
func (s *taskServiceImpl) DeleteTask(ctx context.Context, taskID string) error {
	// 1. 检查任务是否存在
	_, err := s.GetTask(ctx, taskID)
	if err != nil {
		return fmt.Errorf("任务不存在: %w", err)
	}

	// 2. 从状态存储删除任务
	taskKey := fmt.Sprintf("task:%s", taskID)
	err = s.daprClient.DeleteState(ctx, s.config.Dapr.StateStore, taskKey, nil)
	if err != nil {
		return fmt.Errorf("删除任务失败: %w", err)
	}

	// 3. 从任务列表中移除任务ID
	taskListKey := "task:list"
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return fmt.Errorf("获取任务列表失败: %w", err)
	}

	if result.Value != nil {
		var taskIDs []string
		err = json.Unmarshal(result.Value, &taskIDs)
		if err != nil {
			return fmt.Errorf("解析任务列表失败: %w", err)
		}

		// 移除指定的任务ID
		updatedTaskIDs := make([]string, 0, len(taskIDs))
		for _, id := range taskIDs {
			if id != taskID {
				updatedTaskIDs = append(updatedTaskIDs, id)
			}
		}

		// 保存更新后的任务列表
		taskListData, err := json.Marshal(updatedTaskIDs)
		if err != nil {
			return fmt.Errorf("序列化任务列表失败: %w", err)
		}

		err = s.daprClient.SaveState(ctx, s.config.Dapr.StateStore, taskListKey, taskListData, nil)
		if err != nil {
			return fmt.Errorf("保存任务列表失败: %w", err)
		}
	}

	// TODO: 1. 清理相关资源
	// TODO: 2. 发布任务删除事件

	return nil
}

// GetTaskCount 获取任务总数，可选按状态过滤
func (s *taskServiceImpl) GetTaskCount(ctx context.Context, status models.TaskStatus) (int, error) {
	// 获取任务ID列表
	taskListKey := "task:list"
	result, err := s.daprClient.GetState(ctx, s.config.Dapr.StateStore, taskListKey, nil)
	if err != nil {
		return 0, fmt.Errorf("获取任务列表失败: %w", err)
	}

	if result.Value == nil {
		return 0, nil
	}

	var taskIDs []string
	err = json.Unmarshal(result.Value, &taskIDs)
	if err != nil {
		return 0, fmt.Errorf("解析任务列表失败: %w", err)
	}

	// 如果不需要过滤，直接返回总数
	if status == "" {
		return len(taskIDs), nil
	}

	// 需要过滤时，统计符合条件的任务数量
	count := 0
	for _, taskID := range taskIDs {
		task, err := s.GetTask(ctx, taskID)
		if err != nil {
			// 如果某个任务获取失败，记录错误但继续处理其他任务
			log.Printf("获取任务 %s 失败: %v\n", taskID, err)
			continue
		}

		// 如果任务状态匹配，计数加1
		if task.Status == status {
			count++
		}
	}

	return count, nil
}

// Close 关闭Dapr客户端连接和URL队列服务
func (s *taskServiceImpl) Close() error {
	if s.daprClient != nil {
		s.daprClient.Close()
	}
	if s.urlQueueService != nil {
		s.urlQueueService.Close()
	}
	return nil
}
