package services

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"shovel/spider/internal/entity"
	"shovel/spider/internal/models"
	"strings"
	"sync"
	"time"

	"github.com/dapr/go-sdk/client"
	"github.com/dapr/go-sdk/service/common"
	daprd "github.com/dapr/go-sdk/service/http"
)

// TaskExecutorService 任务执行服务
type TaskExecutorService struct {
	daprClient         client.Client
	taskManagerAppID   string
	taskSchedulerAppID string
	httpClient         *http.Client
	pubsubName         string
	topicName          string
	// TaskWorker管理
	taskWorkers map[string]*entity.TaskWorker // TaskID -> TaskWorker映射
	workerMutex sync.RWMutex                  // 保护taskWorkers的读写锁
}

// NewTaskExecutorService 创建任务执行服务
func NewTaskExecutorService(taskManagerAppID, taskSchedulerAppID, pubsubName, topicName string) (*TaskExecutorService, error) {
	daprClient, err := client.NewClient()
	if err != nil {
		return nil, fmt.Errorf("创建Dapr客户端失败: %w", err)
	}

	return &TaskExecutorService{
		daprClient:         daprClient,
		taskManagerAppID:   taskManagerAppID,
		taskSchedulerAppID: taskSchedulerAppID,
		pubsubName:         pubsubName,
		topicName:          topicName,
		httpClient:         &http.Client{
			// 不设置全局超时，使用context控制超时
		},
		taskWorkers: make(map[string]*entity.TaskWorker),
	}, nil
}

// Start 启动任务执行服务
func (d *TaskExecutorService) Start(ctx context.Context) error {
	log.Println("启动任务执行服务...")

	// 从环境变量获取端口号
	port := os.Getenv("SERVER_PORT")
	if port == "" {
		port = "8082" // 默认端口
	}

	// 创建Dapr HTTP服务
	daprSrv := daprd.NewService(":" + port)

	// 订阅Pubsub事件
	subscription := &common.Subscription{
		PubsubName: d.pubsubName,
		Topic:      d.topicName,
		Route:      "/download-url",
	}

	if err := daprSrv.AddTopicEventHandler(subscription, d.handleURLDownload); err != nil {
		return fmt.Errorf("添加事件处理器失败: %w", err)
	}

	log.Println("任务执行服务已启动，等待Pubsub消息...")

	// 启动服务
	return daprSrv.Start()
}

// handleURLDownload 处理URL下载事件
func (d *TaskExecutorService) handleURLDownload(ctx context.Context, e *common.TopicEvent) (retry bool, err error) {
	var urlItem models.URLQueueItem

	// 处理可能的双重JSON编码情况
	var rawJSON string
	if err := json.Unmarshal(e.RawData, &rawJSON); err == nil {
		// 如果成功解析为字符串，说明是双重编码的JSON
		if err := json.Unmarshal([]byte(rawJSON), &urlItem); err != nil {
			log.Printf("解析URL数据失败: %v", err)
			return false, err
		}
	} else {
		// 尝试直接解析
		if err := json.Unmarshal(e.RawData, &urlItem); err != nil {
			log.Printf("解析URL数据失败: %v", err)
			return false, err
		}
	}

	// 验证必要字段
	if urlItem.ID <= 0 {
		log.Printf("无效的URL ID: %d", urlItem.ID)
		return false, fmt.Errorf("无效的URL ID: %d", urlItem.ID)
	}

	if urlItem.URL == "" {
		log.Printf("URL为空: ID=%d", urlItem.ID)
		return false, fmt.Errorf("URL为空: ID=%d", urlItem.ID)
	}

	if urlItem.TaskID == "" {
		log.Printf("URL所属任务ID为空: URL ID=%d", urlItem.ID)
		return false, fmt.Errorf("任务ID不能为空")
	}

	log.Printf("开始处理URL: ID=%d, URL=%s, TaskID=%s", urlItem.ID, urlItem.URL, urlItem.TaskID)

	// 获取任务信息
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	task, err := d.getTaskInfo(timeoutCtx, urlItem.TaskID)
	if err != nil {
		log.Printf("获取URL的任务信息失败: %v", err)
		return false, err
	}
	log.Printf("获取URL的任务信息成功: %v", task)

	// 获取或创建对应TaskID的TaskWorker
	taskWorker, err := d.getOrCreateTaskWorker(urlItem.TaskID, task.MaxConcurrency)
	if err != nil {
		log.Printf("创建TaskWorker失败: %v", err)
		return false, err
	}

	// 爬取func
	crawlTask := func(taskCtx context.Context) {
		var globalToken string
		var needReleaseGlobal bool

		// 如果配置了全局限流器，先尝试获取全局限流资源
		if task.GlobalRateLimiterID != "" {
			log.Printf("尝试获取全局限流资源: limiter_id=%s, url_id=%d", task.GlobalRateLimiterID, urlItem.ID)

			// 使用重试机制获取全局限流资源
			maxRetries := 10
			for i := 0; i < maxRetries; i++ {
				token, allowed, err := d.acquireGlobalRateLimit(taskCtx, task.GlobalRateLimiterID, task.MaxConcurrency, task.Timeout, task.MaxQPS)
				if err != nil {
					log.Printf("获取全局限流资源失败: %v, url_id=%d", err, urlItem.ID)
					// 更新状态为failed
					d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusFailed, fmt.Sprintf("获取全局限流资源失败: %v", err))
					return
				}

				if allowed {
					globalToken = token
					needReleaseGlobal = true
					log.Printf("成功获取全局限流资源: token=%s, url_id=%d", globalToken, urlItem.ID)
					break
				} else {
					log.Printf("全局限流资源不足，等待重试: url_id=%d, retry=%d/%d", urlItem.ID, i+1, maxRetries)
					if i < maxRetries-1 {
						time.Sleep(5 * time.Second) // 等待5秒后重试
					}
				}
			}

			// 如果重试后仍然无法获取资源，标记为失败
			if !needReleaseGlobal {
				log.Printf("达到最大重试次数，无法获取全局限流资源: url_id=%d", urlItem.ID)
				d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusFailed, "无法获取全局限流资源，达到最大重试次数")
				return
			}
		}

		// 确保在函数结束时释放全局限流资源
		defer func() {
			if needReleaseGlobal {
				releaseErr := d.releaseGlobalRateLimit(context.Background(), task.GlobalRateLimiterID, globalToken)
				if releaseErr != nil {
					log.Printf("释放全局限流资源失败: %v, token=%s, url_id=%d", releaseErr, globalToken, urlItem.ID)
				} else {
					log.Printf("成功释放全局限流资源: token=%s, url_id=%d", globalToken, urlItem.ID)
				}
			}
		}()

		// 更新状态为processing
		updateStatusErr := d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusProcessing, "")
		if updateStatusErr != nil {
			log.Printf("更新URL状态为processing失败: %v, id=%d", updateStatusErr, urlItem.ID)
			return
		}

		// 调用Spider服务的crawl接口
		// 超时在URLItem配置的timeout+10秒
		timeout := time.Duration(task.Timeout+10) * time.Second
		timeoutCtx, cancel := context.WithTimeout(taskCtx, timeout)
		defer cancel()
		crawlErr := d.callSpiderCrawl(timeoutCtx, urlItem, task)

		if crawlErr == nil {
			// 更新状态为completed
			for i := 0; i < 3; i++ {
				updateStatusErr = d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusCompleted, "")
				if updateStatusErr == nil {
					break
				}
				log.Printf("更新URL状态为completed失败(尝试 %d/3): %v, id=%d", i+1, updateStatusErr, urlItem.ID)
				time.Sleep(time.Second * time.Duration(i+1)) // 指数退避
			}

			if updateStatusErr != nil {
				log.Printf("更新URL状态为completed最终失败: %v id=%d", updateStatusErr, urlItem.ID)
			} else {
				log.Printf("URL爬取完成: id=%d", urlItem.ID)
			}
		} else {
			log.Printf("URL爬取失败: %v id=%d url=%s", crawlErr, urlItem.ID, urlItem.URL)
			// 更新状态为failed
			for i := 0; i < 3; i++ {
				updateStatusErr = d.updateURLStatus(taskCtx, urlItem.ID, models.URLStatusFailed, crawlErr.Error())
				if updateStatusErr == nil {
					break
				}
				log.Printf("更新URL状态为failed失败(尝试 %d/3): %v, id=%d", i+1, updateStatusErr, urlItem.ID)
				time.Sleep(time.Second * time.Duration(i+1)) // 指数退避
			}

			if updateStatusErr != nil {
				log.Printf("更新URL状态为failed最终失败: %v id=%d", updateStatusErr, urlItem.ID)
			}
		}
	} // crawlTask := func(taskCtx context.Context) end

	// 提交爬取func到TaskWorker
	if !taskWorker.AddTask(crawlTask) {
		log.Printf("提交爬取func到TaskWorker失败: url_id=%d", urlItem.ID)
		return false, fmt.Errorf("提交爬取func到TaskWorker失败")
	}

	// 返回值解释: 是否重试, 错误信息
	return false, nil
}

// getOrCreateTaskWorker 获取或创建指定TaskID的TaskWorker
func (d *TaskExecutorService) getOrCreateTaskWorker(taskID string, maxConcurrency int) (*entity.TaskWorker, error) {
	// 如果taskID为空，直接返回错误
	if taskID == "" {
		return nil, fmt.Errorf("任务ID不能为空")
	}

	// 先尝试从map中获取现有TaskWorker
	d.workerMutex.RLock()
	worker, exists := d.taskWorkers[taskID]
	d.workerMutex.RUnlock()

	if exists {
		return worker, nil
	}

	// 不存在，则创建新的TaskWorker
	d.workerMutex.Lock()
	defer d.workerMutex.Unlock()

	// 双重检查，防止在获取写锁期间其他goroutine已创建
	if worker, exists = d.taskWorkers[taskID]; exists {
		return worker, nil
	}

	// 检查并发数是否有效
	if maxConcurrency <= 0 {
		return nil, fmt.Errorf("无效的并发数配置: %d", maxConcurrency)
	}

	// 创建新的TaskWorker，缓冲区大小设为并发数的10倍
	worker = entity.NewTaskWorker(maxConcurrency, maxConcurrency*10)
	worker.Start()

	// 存储到map中
	d.taskWorkers[taskID] = worker
	log.Printf("成功新创建TaskWorker: TaskID=%s, MaxConcurrency=%d", taskID, maxConcurrency)

	return worker, nil
}

// getTaskInfo 通过Dapr调用获取任务信息
func (d *TaskExecutorService) getTaskInfo(ctx context.Context, taskID string) (*models.CrawlerTask, error) {
	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/tasks/%s", taskID)
	// 使用 HTTP 客户端代理模式替代 InvokeMethod
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", d.taskManagerAppID, methodPath)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	resp, err := d.httpClient.Do(req)
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return nil, fmt.Errorf("调用服务超时: %w", err)
		} else if ctx.Err() == context.Canceled {
			return nil, fmt.Errorf("调用服务请求被取消: %w", err)
		}
		return nil, fmt.Errorf("调用服务失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取服务响应失败: %w", err)
	}

	// 解析响应结构体
	var response struct {
		Success bool               `json:"success"`
		Data    models.CrawlerTask `json:"data"`
		Error   string             `json:"error"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("解析服务响应失败: %w 响应体=%s", err, string(respBody))
	}

	// 检查响应是否成功
	if !response.Success {
		return nil, fmt.Errorf("获取任务信息失败: %s", response.Error)
	}

	return &response.Data, nil
}

// updateURLStatus 更新URL状态
func (d *TaskExecutorService) updateURLStatus(ctx context.Context, urlID int64, status models.URLStatus, errorMessage string) error {
	// 验证状态值是否有效
	validStatuses := map[models.URLStatus]bool{
		models.URLStatusPending:    true,
		models.URLStatusScheduled:  true,
		models.URLStatusProcessing: true,
		models.URLStatusCompleted:  true,
		models.URLStatusFailed:     true,
	}

	if !validStatuses[status] {
		return fmt.Errorf("无效的状态值: %s", status)
	}

	// 构建请求体
	payload := map[string]interface{}{
		"status": string(status), // 确保使用字符串格式
	}
	if errorMessage != "" {
		payload["error_message"] = errorMessage
	}

	data, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 使用 HTTP 客户端代理模式替代 InvokeMethodWithContent
	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	// 构建请求路径
	methodPath := fmt.Sprintf("api/v1/urls/%d/status", urlID)
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", d.taskManagerAppID, methodPath)

	req, err := http.NewRequestWithContext(timeoutCtx, "PUT", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			return fmt.Errorf("调用服务超时: %w", err)
		} else if timeoutCtx.Err() == context.Canceled {
			return fmt.Errorf("调用服务请求被取消: %w", err)
		} else {
			return fmt.Errorf("调用服务失败: %w 请求体=%s", err, string(data))
		}
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取服务响应失败: %w", err)
	}

	// 尝试解析错误响应
	if resp.StatusCode >= 400 {
		var errResp struct {
			Success bool   `json:"success"`
			Message string `json:"message"`
			Error   string `json:"error"`
		}

		if len(respBody) > 0 {
			if jsonErr := json.Unmarshal(respBody, &errResp); jsonErr == nil {
				return fmt.Errorf("服务返回错误: 状态码=%d 消息=%s 错误=%s", resp.StatusCode, errResp.Message, errResp.Error)
			} else {
				return fmt.Errorf("解析服务错误响应失败: %w 状态码=%d 原始响应=%s", jsonErr, resp.StatusCode, string(respBody))
			}
		} else {
			return fmt.Errorf("服务返回错误: 状态码=%d", resp.StatusCode)
		}
	}

	return nil
}

// callSpiderCrawl 调用Spider服务的crawl接口
func (d *TaskExecutorService) callSpiderCrawl(ctx context.Context, urlItem models.URLQueueItem, task *models.CrawlerTask) error {
	// 序列化URL项
	data, err := json.Marshal(urlItem)
	if err != nil {
		return fmt.Errorf("序列化URL数据失败: %w", err)
	}

	// 使用任务中的spiderName作为Spider服务的应用ID
	spiderAppID := task.SpiderName

	// 使用 HTTP 客户端代理模式替代 InvokeMethodWithContent
	url := fmt.Sprintf("http://localhost:3500/v1.0/invoke/%s/method/%s", spiderAppID, "crawl")

	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(req)
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return fmt.Errorf("调用服务超时: %w", err)
		} else if ctx.Err() == context.Canceled {
			return fmt.Errorf("调用服务请求被取消: %w", err)
		}
		return fmt.Errorf("调用服务失败: %w 请求体=%s", err, string(data))
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取服务响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode >= 400 {
		return fmt.Errorf("服务返回错误: 状态码=%d 响应体=%s", resp.StatusCode, strings.TrimSpace(string(respBody)))
	}

	// 解析响应
	var response struct {
		Success bool                  `json:"success"`
		Message string                `json:"message"`
		URLID   int64                 `json:"url_id,omitempty"`
		URLs    []models.URLQueueItem `json:"extracted_urls,omitempty"`
	}

	if err := json.Unmarshal(respBody, &response); err != nil {
		return fmt.Errorf("解析服务响应失败: %w 响应体=%s", err, string(respBody))
	}

	if !response.Success {
		return fmt.Errorf("服务返回错误: 消息=%s", response.Message)
	}

	return nil
}

// generateToken 生成唯一token
func (d *TaskExecutorService) generateToken() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// acquireGlobalRateLimit 获取全局限流资源
func (d *TaskExecutorService) acquireGlobalRateLimit(ctx context.Context, limiterID string, maxConcurrency, timeoutSeconds int, maxQPS float64) (string, bool, error) {
	if limiterID == "" {
		// 如果没有配置全局限流器ID，直接返回成功
		return "", true, nil
	}

	token := d.generateToken()

	req := &models.GlobalRateLimiterRequest{
		LimiterID:      limiterID,
		Token:          token,
		MaxConcurrency: maxConcurrency,
		MaxQPS:         maxQPS,
		TimeoutSeconds: timeoutSeconds,
	}

	data, err := json.Marshal(req)
	if err != nil {
		return "", false, fmt.Errorf("序列化全局限流请求失败: %w", err)
	}

	// 调用task-scheduler的全局限流接口
	url := fmt.Sprintf("http://localhost:3501/v1.0/invoke/%s/method/api/v1/rate-limiter/acquire", d.taskSchedulerAppID)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return "", false, fmt.Errorf("创建全局限流请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(httpReq)
	if err != nil {
		return "", false, fmt.Errorf("调用全局限流接口失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", false, fmt.Errorf("读取全局限流响应失败: %w", err)
	}

	var response models.GlobalRateLimiterResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return "", false, fmt.Errorf("解析全局限流响应失败: %w", err)
	}

	if !response.Success {
		return "", false, fmt.Errorf("全局限流请求失败: %s", response.Error)
	}

	return token, response.Allowed, nil
}

// releaseGlobalRateLimit 释放全局限流资源
func (d *TaskExecutorService) releaseGlobalRateLimit(ctx context.Context, limiterID, token string) error {
	if limiterID == "" || token == "" {
		// 如果没有配置全局限流器ID或token，直接返回成功
		return nil
	}

	req := &models.ReleaseRateLimiterRequest{
		LimiterID: limiterID,
		Token:     token,
	}

	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化释放限流请求失败: %w", err)
	}

	// 调用task-scheduler的释放限流接口
	url := fmt.Sprintf("http://localhost:3501/v1.0/invoke/%s/method/api/v1/rate-limiter/release", d.taskSchedulerAppID)
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(data))
	if err != nil {
		return fmt.Errorf("创建释放限流请求失败: %w", err)
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := d.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("调用释放限流接口失败: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取释放限流响应失败: %w", err)
	}

	var response models.GlobalRateLimiterResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return fmt.Errorf("解析释放限流响应失败: %w", err)
	}

	if !response.Success {
		return fmt.Errorf("释放限流请求失败: %s", response.Error)
	}

	return nil
}

// Close 关闭服务
func (d *TaskExecutorService) Close() error {
	// 停止所有TaskWorker
	d.workerMutex.Lock()
	for taskID, worker := range d.taskWorkers {
		log.Printf("正在停止TaskWorker: TaskID=%s", taskID)
		worker.Stop()
	}
	// 清空map
	d.taskWorkers = make(map[string]*entity.TaskWorker)
	d.workerMutex.Unlock()

	// 关闭Dapr客户端
	if d.daprClient != nil {
		d.daprClient.Close()
	}

	log.Println("任务执行服务已关闭")
	return nil
}
