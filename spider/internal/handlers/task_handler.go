package handlers

import (
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"shovel/spider/internal/models"
	"shovel/spider/internal/services"

	"github.com/gin-gonic/gin"
)

// TaskHandler 任务处理器
type TaskHandler struct {
	taskService services.TaskService
}

// NewTaskHandler 创建新的任务处理器
func NewTaskHandler(taskService services.TaskService) *TaskHandler {
	return &TaskHandler{
		taskService: taskService,
	}
}

// CreateTask 创建新的爬虫任务
// @Summary 创建爬虫任务
// @Description 创建一个新的爬虫任务
// @Tags tasks
// @Accept json
// @Produce json
// @Param task body models.CreateTaskRequest true "任务信息"
// @Success 201 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks [post]
func (h *TaskHandler) CreateTask(c *gin.Context) {
	var req models.CreateTaskRequest

	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 验证初始URL列表
	for i, url := range req.InitialURLs {
		if url.Method == models.POST && url.Body == "" {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "POST请求必须包含请求体",
				Error:   "initial_urls[" + strconv.Itoa(i) + "].body is required for POST method",
			})
			return
		}
	}

	// 验证下载配置JSON格式
	if req.DownloadConfig != "" {
		var downloadConfig map[string]any
		if err := json.Unmarshal([]byte(req.DownloadConfig), &downloadConfig); err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "下载配置JSON格式无效",
				Error:   err.Error(),
			})
			return
		}
	}

	// 验证解析配置JSON格式
	if req.ParseConfig != "" {
		var parseConfig map[string]any
		if err := json.Unmarshal([]byte(req.ParseConfig), &parseConfig); err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "解析配置JSON格式无效",
				Error:   err.Error(),
			})
			return
		}
	}

	// 调用服务层创建任务
	task, err := h.taskService.CreateTask(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "创建任务失败",
			Error:   err.Error(),
		})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusCreated, models.ErrorResponse{
		Success: true,
		Message: "任务创建成功",
		Data:    task,
	})
}

// GetTask 获取任务信息
// @Summary 获取任务信息
// @Description 根据任务ID获取任务详细信息
// @Tags tasks
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id} [get]
func (h *TaskHandler) GetTask(c *gin.Context) {
	taskID := c.Param("id")

	task, err := h.taskService.GetTask(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "任务不存在",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "获取任务成功",
		Data:    task,
	})
}

// ListTasks 获取任务列表
// @Summary 获取任务列表
// @Description 获取爬虫任务列表，支持分页和状态过滤
// @Tags tasks
// @Produce json
// @Param limit query int false "每页数量限制，默认10"
// @Param offset query int false "偏移量，默认0"
// @Param status query string false "任务状态过滤，可选值: pending, running, completed, failed, paused, deleted"
// @Success 200 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks [get]
func (h *TaskHandler) ListTasks(c *gin.Context) {
	limit := 10
	offset := 0

	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 {
			limit = parsed
		}
	}

	if o := c.Query("offset"); o != "" {
		if parsed, err := strconv.Atoi(o); err == nil && parsed >= 0 {
			offset = parsed
		}
	}

	// 获取状态过滤参数
	status := models.TaskStatus(c.Query("status"))

	// 验证状态值是否有效
	if status != "" {
		validStatuses := map[models.TaskStatus]bool{
			models.StatusPending:   true,
			models.StatusRunning:   true,
			models.StatusCompleted: true,
			models.StatusFailed:    true,
			models.StatusPaused:    true,
			models.StatusDeleted:   true,
		}

		if !validStatuses[status] {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "无效的状态值",
				Error:   "status must be one of: pending, running, completed, failed, paused, deleted",
			})
			return
		}
	}

	tasks, err := h.taskService.ListTasks(c.Request.Context(), limit, offset, status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取任务列表失败",
			Error:   err.Error(),
		})
		return
	}

	// 获取过滤后的任务数量
	total, err := h.taskService.GetTaskCount(c.Request.Context(), status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "获取任务总数失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "获取任务列表成功",
		Data: map[string]interface{}{
			"tasks": tasks,
			"total": total,
		},
	})
}

// UpdateTaskStatus 更新任务状态
// @Summary 更新任务状态
// @Description 更新指定任务的状态
// @Tags tasks
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Param status body object true "状态信息"
// @Success 200 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id}/status [put]
func (h *TaskHandler) UpdateTaskStatus(c *gin.Context) {
	taskID := c.Param("id")

	var req struct {
		Status models.TaskStatus `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 验证状态值是否有效
	validStatuses := map[models.TaskStatus]bool{
		models.StatusPending:   true,
		models.StatusRunning:   true,
		models.StatusCompleted: true,
		models.StatusFailed:    true,
		models.StatusPaused:    true,
		models.StatusDeleted:   true,
	}

	if !validStatuses[req.Status] {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "无效的状态值",
			Error:   "status must be one of: pending, running, completed, failed, paused, deleted",
		})
		return
	}

	err := h.taskService.UpdateTaskStatus(c.Request.Context(), taskID, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "更新任务状态失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务状态更新成功",
	})
}

// UpdateTask 更新任务信息
// @Summary 更新任务信息
// @Description 更新指定任务的信息（如LastExecutedAt等）
// @Tags tasks
// @Accept json
// @Produce json
// @Param id path string true "任务ID"
// @Param task body object true "任务信息"
// @Success 200 {object} models.ErrorResponse
// @Failure 400 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id} [put]
func (h *TaskHandler) UpdateTask(c *gin.Context) {
	taskID := c.Param("id")

	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.ErrorResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 处理时间字段
	if lastExecutedAtStr, ok := req["last_executed_at"].(string); ok {
		lastExecutedAt, err := time.Parse(time.RFC3339, lastExecutedAtStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "无效的时间格式",
				Error:   "last_executed_at 应为RFC3339格式",
			})
			return
		}
		req["last_executed_at"] = lastExecutedAt
	}

	// 验证下载配置JSON格式
	if downloadConfigStr, ok := req["download_config"].(string); ok && downloadConfigStr != "" {
		var downloadConfig map[string]any
		if err := json.Unmarshal([]byte(downloadConfigStr), &downloadConfig); err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "下载配置JSON格式无效",
				Error:   err.Error(),
			})
			return
		}
	}

	// 验证解析配置JSON格式
	if parseConfigStr, ok := req["parse_config"].(string); ok && parseConfigStr != "" {
		var parseConfig map[string]any
		if err := json.Unmarshal([]byte(parseConfigStr), &parseConfig); err != nil {
			c.JSON(http.StatusBadRequest, models.ErrorResponse{
				Success: false,
				Message: "解析配置JSON格式无效",
				Error:   err.Error(),
			})
			return
		}
	}

	err := h.taskService.UpdateTask(c.Request.Context(), taskID, req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "更新任务信息失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务信息更新成功",
	})
}

// DeleteTask 删除任务
// @Summary 删除任务
// @Description 删除指定的爬虫任务
// @Tags tasks
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id} [delete]
func (h *TaskHandler) DeleteTask(c *gin.Context) {
	taskID := c.Param("id")

	err := h.taskService.DeleteTask(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "删除任务失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务删除成功",
	})
}

// StartTask 启动任务
// @Summary 启动任务
// @Description 启动指定的爬虫任务，将状态更新为running
// @Tags tasks
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id}/start [post]
func (h *TaskHandler) StartTask(c *gin.Context) {
	taskID := c.Param("id")

	// 首先检查任务是否存在
	task, err := h.taskService.GetTask(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "任务不存在",
			Error:   err.Error(),
		})
		return
	}

	// 更新任务状态为运行中
	err = h.taskService.UpdateTaskStatus(c.Request.Context(), taskID, models.StatusRunning)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "启动任务失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务启动成功",
		Data:    task,
	})
}

// PauseTask 暂停任务
// @Summary 暂停任务
// @Description 暂停指定的爬虫任务，将状态更新为paused
// @Tags tasks
// @Produce json
// @Param id path string true "任务ID"
// @Success 200 {object} models.ErrorResponse
// @Failure 404 {object} models.ErrorResponse
// @Failure 500 {object} models.ErrorResponse
// @Router /api/v1/tasks/{id}/pause [post]
func (h *TaskHandler) PauseTask(c *gin.Context) {
	taskID := c.Param("id")

	// 首先检查任务是否存在
	task, err := h.taskService.GetTask(c.Request.Context(), taskID)
	if err != nil {
		c.JSON(http.StatusNotFound, models.ErrorResponse{
			Success: false,
			Message: "任务不存在",
			Error:   err.Error(),
		})
		return
	}

	// 更新任务状态为暂停
	err = h.taskService.UpdateTaskStatus(c.Request.Context(), taskID, models.StatusPaused)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.ErrorResponse{
			Success: false,
			Message: "暂停任务失败",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, models.ErrorResponse{
		Success: true,
		Message: "任务暂停成功",
		Data:    task,
	})
}
