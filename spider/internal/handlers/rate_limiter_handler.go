package handlers

import (
	"net/http"
	"shovel/spider/internal/models"
	"shovel/spider/internal/services"

	"github.com/gin-gonic/gin"
)

// RateLimiterHandler 全局限流处理器
type RateLimiterHandler struct {
	rateLimiterService *services.RateLimiterService
}

// NewRateLimiterHandler 创建全局限流处理器
func NewRateLimiterHandler(rateLimiterService *services.RateLimiterService) *RateLimiterHandler {
	return &RateLimiterHandler{
		rateLimiterService: rateLimiterService,
	}
}

// AcquireResource 获取并发资源
// @Summary 获取全局限流资源
// @Description 请求获取全局限流器的并发资源
// @Tags rate-limiter
// @Accept json
// @Produce json
// @Param request body models.GlobalRateLimiterRequest true "获取资源请求"
// @Success 200 {object} models.GlobalRateLimiterResponse
// @Failure 400 {object} models.GlobalRateLimiterResponse
// @Failure 500 {object} models.GlobalRateLimiterResponse
// @Router /api/v1/rate-limiter/acquire [post]
func (h *RateLimiterHandler) AcquireResource(c *gin.Context) {
	var req models.GlobalRateLimiterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.GlobalRateLimiterResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 调用服务层获取资源
	resp, err := h.rateLimiterService.AcquireResource(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.GlobalRateLimiterResponse{
			Success: false,
			Message: "获取资源失败",
			Error:   err.Error(),
		})
		return
	}

	// 根据是否允许返回不同的HTTP状态码
	statusCode := http.StatusOK
	if resp.Success && !resp.Allowed {
		statusCode = http.StatusTooManyRequests // 429状态码表示请求过多
	} else if !resp.Success {
		statusCode = http.StatusInternalServerError
	}

	c.JSON(statusCode, resp)
}

// ReleaseResource 释放并发资源
// @Summary 释放全局限流资源
// @Description 释放之前获取的全局限流器并发资源
// @Tags rate-limiter
// @Accept json
// @Produce json
// @Param request body models.ReleaseRateLimiterRequest true "释放资源请求"
// @Success 200 {object} models.GlobalRateLimiterResponse
// @Failure 400 {object} models.GlobalRateLimiterResponse
// @Failure 500 {object} models.GlobalRateLimiterResponse
// @Router /api/v1/rate-limiter/release [post]
func (h *RateLimiterHandler) ReleaseResource(c *gin.Context) {
	var req models.ReleaseRateLimiterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.GlobalRateLimiterResponse{
			Success: false,
			Message: "请求参数无效",
			Error:   err.Error(),
		})
		return
	}

	// 调用服务层释放资源
	resp, err := h.rateLimiterService.ReleaseResource(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, models.GlobalRateLimiterResponse{
			Success: false,
			Message: "释放资源失败",
			Error:   err.Error(),
		})
		return
	}

	statusCode := http.StatusOK
	if !resp.Success {
		statusCode = http.StatusInternalServerError
	}

	c.JSON(statusCode, resp)
}
