package parser

import (
	"context"
	"shovel/spider/internal/models"
)

// ParseResult 定义解析结果
type ParseResult struct {
	Items []*models.URLQueueItem // 解析出的URL队列项
	Data  interface{}            // 其他解析数据
}

// Parser 定义解析器接口
type Parser interface {
	// Parse 解析下载内容
	// content: 下载的内容
	// urlItem: URL队列项，包含任务ID和其他元数据
	// parseConfig: 任务的解析配置
	// 返回解析结果和错误
	Parse(ctx context.Context, content []byte, urlItem *models.URLQueueItem, parseConfig string) (*ParseResult, error)
}
