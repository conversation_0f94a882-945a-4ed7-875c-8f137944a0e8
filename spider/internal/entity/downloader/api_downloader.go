package downloader

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"shovel/spider/internal/models"
)

// APIDownloader 实现用于下载JSON API的下载器
type APIDownloader struct {
	client    *http.Client
	userAgent string
}

// NewAPIDownloader 创建新的API下载器实例
func NewAPIDownloader(userAgent string) *APIDownloader {
	return &APIDownloader{
		client: &http.Client{
			Timeout: 30 * time.Second, // 默认超时时间
		},
		userAgent: userAgent,
	}
}

// Download 实现Downloader接口的Download方法
func (d *APIDownloader) Download(ctx context.Context, urlItem *models.URLQueueItem, timeout int) (*DownloadResult, error) {
	// 创建带有超时的上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	defer cancel()

	startTime := time.Now()

	// 创建HTTP请求
	var req *http.Request
	var err error

	if urlItem.Method == models.POST && urlItem.Body != "" {
		req, err = http.NewRequestWithContext(timeoutCtx, string(urlItem.Method), urlItem.URL, bytes.NewBufferString(urlItem.Body))
	} else {
		req, err = http.NewRequestWithContext(timeoutCtx, string(urlItem.Method), urlItem.URL, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("User-Agent", d.userAgent)
	req.Header.Set("Accept", "application/json")

	if urlItem.Method == models.POST && urlItem.Body != "" {
		req.Header.Set("Content-Type", "application/json")
	}

	// 发送请求
	resp, err := d.client.Do(req)
	if err != nil {
		if timeoutCtx.Err() == context.DeadlineExceeded {
			return nil, fmt.Errorf("请求超时: %w", err)
		}
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应内容
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	// 提取响应头
	headers := make(map[string]string)
	for key, values := range resp.Header {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	downloadTime := time.Since(startTime)

	// 返回下载结果
	return &DownloadResult{
		Content:      content,
		ContentType:  resp.Header.Get("Content-Type"),
		StatusCode:   resp.StatusCode,
		Headers:      headers,
		DownloadTime: downloadTime,
	}, nil
}
