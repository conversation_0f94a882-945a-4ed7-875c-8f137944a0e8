{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 5, "links": [], "panels": [{"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "id": 2, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"dapr_apps\"}", "refId": "A"}], "title": "应用服务日志", "type": "logs"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"dapr_sidecar\"}", "refId": "A"}], "title": "Dapr Sidecar日志", "type": "logs"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 4, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"varlogs\"}", "refId": "A"}], "title": "系统日志", "type": "logs"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 5, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"dapr_apps\", app=\"task-manager\"}", "refId": "A"}], "title": "任务管理服务日志", "type": "logs"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 6, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"dapr_apps\", app=\"task-scheduler\"}", "refId": "A"}], "title": "URL调度器服务日志", "type": "logs"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 7, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"dapr_apps\", app=\"task-executor\"}", "refId": "A"}], "title": "任务执行器服务日志", "type": "logs"}, {"datasource": "<PERSON>", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 8, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"expr": "{job=\"dapr_apps\", app=\"spider\"}", "refId": "A"}], "title": "Spider服务日志", "type": "logs"}], "schemaVersion": 27, "style": "dark", "tags": ["dapr", "logs"], "templating": {"list": [{"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "<PERSON>", "definition": "label_values(app)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "应用", "multi": false, "name": "app", "options": [], "query": "label_values(app)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Dapr应用日志", "uid": "dapr-logs", "version": 1}