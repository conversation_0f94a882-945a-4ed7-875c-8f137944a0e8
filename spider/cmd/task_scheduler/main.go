package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"shovel/spider/internal/handlers"
	"shovel/spider/internal/services"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	log.Println("启动task-scheduler服务...")

	// 从环境变量获取配置，如果没有则使用默认值
	taskManagerAppID := getEnv("TASK_MANAGER_APP_ID", "task-manager")
	pubsubName := getEnv("PUBSUB_NAME", "pubsub")
	topicName := getEnv("TOPIC_NAME", "url-download")
	serverPort := getEnv("SERVER_PORT", "8081")

	// 创建URL调度服务
	scheduler, err := services.NewURLSchedulerService(taskManagerAppID, pubsubName, topicName)
	if err != nil {
		log.Fatalf("创建URL调度服务失败: %v", err)
	}
	defer scheduler.Close()

	// 创建全局限流服务
	rateLimiterService, err := services.NewRateLimiterService()
	if err != nil {
		log.Fatalf("创建全局限流服务失败: %v", err)
	}
	defer rateLimiterService.Close()

	// 创建处理器
	rateLimiterHandler := handlers.NewRateLimiterHandler(rateLimiterService)

	// 创建HTTP路由器
	router := setupRoutes(rateLimiterHandler)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    ":" + serverPort,
		Handler: router,
	}

	// 启动URL调度服务
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := scheduler.Start(ctx); err != nil {
		log.Fatalf("启动URL调度服务失败: %v", err)
	}

	// 启动HTTP服务器
	go func() {
		fmt.Printf("启动task-scheduler HTTP服务，端口: %s\n", serverPort)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动HTTP服务失败: %v", err)
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("正在关闭服务器...")

	// 优雅关闭服务器，设置5秒超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer shutdownCancel()

	if err := server.Shutdown(shutdownCtx); err != nil {
		log.Printf("HTTP服务器关闭失败，强制关闭: %v", err)
	}

	// 关闭URL调度服务
	cancel()

	fmt.Println("服务器已关闭")
}

// setupRoutes 设置路由
func setupRoutes(rateLimiterHandler *handlers.RateLimiterHandler) *gin.Engine {
	// 创建Gin路由器
	router := gin.Default()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "healthy",
			"service": "task-scheduler",
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 全局限流接口
		rateLimiter := v1.Group("/rate-limiter")
		{
			rateLimiter.POST("/acquire", rateLimiterHandler.AcquireResource)
			rateLimiter.POST("/release", rateLimiterHandler.ReleaseResource)
		}
	}

	return router
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
