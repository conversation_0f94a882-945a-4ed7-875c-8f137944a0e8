package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"shovel/spider/config"
	"shovel/spider/internal/handlers"
	"shovel/spider/internal/services"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	// 初始化任务服务层
	taskService, err := services.NewTaskService(cfg)
	if err != nil {
		log.Fatalf("初始化任务服务失败: %v", err)
	}
	defer func() {
		if err := taskService.Close(); err != nil {
			log.Printf("关闭任务服务失败: %v", err)
		}
	}()

	// 获取URL队列服务层（从任务服务中获取）
	urlQueueService, err := services.NewURLQueueService(cfg)
	if err != nil {
		log.Fatalf("初始化URL队列服务失败: %v", err)
	}
	defer func() {
		if err := urlQueueService.Close(); err != nil {
			log.Printf("关闭URL队列服务失败: %v", err)
		}
	}()

	// 初始化任务接入点层处理器
	taskHandler := handlers.NewTaskHandler(taskService)

	// 初始化URL队列接入点层处理器
	urlQueueHandler := handlers.NewURLQueueHandler(urlQueueService)

	// 创建Gin路由器
	router := setupRoutes(taskHandler, urlQueueHandler)

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    ":" + cfg.Dapr.AppPort,
		Handler: router,
	}

	// 启动服务器
	go func() {
		fmt.Printf("启动爬虫任务管理服务，端口: %s\n", cfg.Dapr.AppPort)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动HTTP服务失败: %v", err)
		}
	}()

	// 等待中断信号以优雅关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("正在关闭服务器...")

	// 优雅关闭服务器，设置5秒超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("HTTP服务器关闭失败，强制关闭: %v", err)
	}

	fmt.Println("服务器已关闭")
}

// setupRoutes 设置路由
func setupRoutes(taskHandler *handlers.TaskHandler, urlQueueHandler *handlers.URLQueueHandler) *gin.Engine {
	// 创建Gin路由器
	router := gin.Default()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
		})
	})

	// API版本1路由组
	v1 := router.Group("/api/v1")
	{
		// 任务相关路由
		tasks := v1.Group("/tasks")
		{
			tasks.POST("", taskHandler.CreateTask)                 // 创建任务
			tasks.GET("", taskHandler.ListTasks)                   // 获取任务列表
			tasks.GET("/:id", taskHandler.GetTask)                 // 获取单个任务
			tasks.PUT("/:id/status", taskHandler.UpdateTaskStatus) // 更新任务状态
			tasks.POST("/:id/start", taskHandler.StartTask)        // 启动任务
			tasks.PUT("/:id", taskHandler.UpdateTask)              // 更新任务信息
			tasks.DELETE("/:id", taskHandler.DeleteTask)           // 删除任务

			// 任务URL统计 - 使用子路由组避免路径冲突
			taskUrls := tasks.Group("/:id/urls")
			{
				taskUrls.GET("/stats", urlQueueHandler.GetTaskURLStats)                 // 获取任务URL统计
				taskUrls.POST("/reset", urlQueueHandler.ResetFailedURLs)                // 重置失败的URL
				taskUrls.POST("/reset-processing", urlQueueHandler.ResetProcessingURLs) // 重置处理中和已调度的URL为失败状态
			}
		}

		// URL队列相关路由
		urls := v1.Group("/urls")
		{
			urls.POST("", urlQueueHandler.AddURLsToQueue)            // 添加URL到队列
			urls.GET("/pending", urlQueueHandler.GetPendingURLs)     // 获取待处理的URL
			urls.GET("", urlQueueHandler.QueryURLs)                  // 查询URL队列
			urls.PUT("/:id/status", urlQueueHandler.UpdateURLStatus) // 更新URL状态
		}
	}

	// 根路径信息
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "爬虫任务管理服务",
			"version": "1.0.1",
			"status":  "running",
		})
	})

	return router
}

// corsMiddleware CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			c.Header("Access-Control-Allow-Origin", origin)
		} else {
			c.Header("Access-Control-Allow-Origin", "*")
		}
		//c.Header("Access-Control-Allow-Credentials", "true")
		//c.Header("Access-Control-Allow-Headers", "*")
		c.Header("Access-Control-Allow-Headers", "dapr-app-id, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")
		c.Header("Access-Control-Expose-Headers", "Content-Length")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}
