package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"shovel/spider/config"
	"shovel/spider/internal/handlers"
	"shovel/spider/internal/services"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg := config.LoadConfig()

	// 创建爬虫管理服务
	spiderManagerService, err := services.NewSpiderManagerService(cfg)
	if err != nil {
		log.Fatalf("创建爬虫管理服务失败: %v", err)
	}
	defer spiderManagerService.Close()

	// 创建爬虫处理器
	spiderHandler := handlers.NewSpiderHandler(spiderManagerService)

	// 创建Gin路由
	router := gin.Default()

	// 简单的CORS中间件
	router.Use(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
		}
		c.Writer.Header().Set("Access-Control-Allow-Credentials", "true")
		c.Writer.Header().Set("Access-Control-Allow-Headers", "*")
		c.Writer.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Writer.Header().Set("Access-Control-Expose-Headers", "Content-Length")
		//c.Writer.Header().Set("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})

	// 注册API路由
	apiV1 := router.Group("/api/v1")
	{
		// 爬虫管理API
		spiders := apiV1.Group("/spiders")
		{
			spiders.GET("", spiderHandler.ListSpiders)           // 获取爬虫列表
			spiders.GET("/:name", spiderHandler.GetSpider)       // 获取爬虫详情
			spiders.POST("", spiderHandler.RegisterSpider)       // 注册爬虫
			spiders.PUT("/:name", spiderHandler.UpdateSpider)    // 更新爬虫
			spiders.DELETE("/:name", spiderHandler.DeleteSpider) // 删除爬虫
		}
	}

	// 启动HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf("%s:%s", cfg.Server.Host, cfg.Server.Port),
		Handler: router,
	}

	// 在goroutine中启动服务器，以便不阻塞优雅关闭处理
	go func() {
		log.Printf("爬虫管理服务已启动，监听地址: %s:%s", cfg.Server.Host, cfg.Server.Port)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("关闭服务器...")

	// 设置5秒的超时时间用于关闭服务器
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	if err := srv.Shutdown(ctx); err != nil {
		log.Fatalf("服务器关闭失败: %v", err)
	}

	log.Println("服务器已优雅关闭")
}
