version: 1
common:
  resourcesPath: ./components
  configFilePath: ./config.yaml

apps:
  # 爬虫任务管理服务
  - appID: task-manager
    appDirPath: ../
    appPort: 8080
    command: ["./bin/task-manager"]  # 使用编译后的二进制文件
    env:
      # 服务器配置
      SERVER_PORT: "8080"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "task-manager"
      DAPR_APP_PORT: "8080"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"
      
    # Dapr sidecar配置
    daprHTTPPort: 3500
    daprGRPCPort: 35000
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6090
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

  # 任务调度器服务
  - appID: task-scheduler
    appDirPath: ../
    appPort: 8081
    command: ["./bin/task-scheduler"]  # 使用编译后的二进制文件
    env:
      # 服务器配置
      SERVER_PORT: "8081"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "task-scheduler"
      DAPR_APP_PORT: "8081"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"
      
    # Dapr sidecar配置
    daprHTTPPort: 3501
    daprGRPCPort: 35001
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6091
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

  # 任务执行器服务
  - appID: task-executor
    appDirPath: ../
    appPort: 8082
    command: ["./bin/task-executor"]  # 使用编译后的二进制文件
    env:
      # 服务器配置
      SERVER_PORT: "8082"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "task-executor"
      DAPR_APP_PORT: "8082"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"
      
    # Dapr sidecar配置
    daprHTTPPort: 3502
    daprGRPCPort: 35002
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6092
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3


  - appID: spider-manager
    appDirPath: ../
    appPort: 8083
    command: ["./bin/spider-manager"]
    env:
      SERVER_PORT: "8083"
      SERVER_HOST: "0.0.0.0"

      # Dapr配置
      DAPR_APP_ID: "spider-manager"
      DAPR_APP_PORT: "8083"
      DAPR_STATE_STORE: "statestore-mongodb"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"

      # MongoDB配置
      MONGO_URI: "mongodb://localhost:27017"
      MONGO_DATABASE: "crawler_db"

      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"

    # Dapr sidecar配置
    daprHTTPPort: 3503
    daprGRPCPort: 35003
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6093


  # JSON_API-Spider服务
  - appID: JSON_API-Spider
    appDirPath: ../
    appPort: 8090
    command: ["./bin/spider"]  # 使用编译后的二进制文件
    env:
      # 服务器配置
      SERVER_PORT: "8090"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "JSON_API-Spider"
      DAPR_APP_PORT: "8090"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"
      
      # 爬虫配置
      DOWNLOADER_TYPE: "json_api"
      PARSER_TYPE: "json_api"
      
      # MongoDB配置
      MONGO_URI: "mongodb://localhost:27017"
      MONGO_DATABASE: "crawler_db"
      MONGO_COLLECTION: "json_api_data"

      # MySQL配置
      MYSQL_DSN: "crawler_user:jAhp6qFWXj@tcp(*********:3306)/crawler_db?charset=utf8mb4&parseTime=True&loc=Local"
      MYSQL_TABLE: "json_api_data"
      
    # Dapr sidecar配置
    daprHTTPPort: 3510
    daprGRPCPort: 35010
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6094
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3

    # File-Spider服务
  - appID: File-Spider
    appDirPath: ../
    appPort: 8091
    command: ["./bin/spider"]  # 使用编译后的二进制文件
    env:
      # 服务器配置
      SERVER_PORT: "8091"
      SERVER_HOST: "0.0.0.0"
      
      # Dapr配置
      DAPR_APP_ID: "File-Spider"
      DAPR_APP_PORT: "8091"
      DAPR_STATE_STORE: "statestore"
      DAPR_PUBSUB_NAME: "pubsub"
      DAPR_SECRET_STORE: "secretstore"
      
      # 生产环境配置
      GIN_MODE: "release"
      LOG_LEVEL: "info"
      
      # 爬虫配置
      DOWNLOADER_TYPE: "html"
      PARSER_TYPE: "file"
      FILE_SAVE_DIR: "./downloads"

    # Dapr sidecar配置
    daprHTTPPort: 3511
    daprGRPCPort: 35011
    daprHTTPMaxRequestSize: 4
    daprHTTPReadBufferSize: 4
    enableProfiling: true
    logLevel: info
    appLogDestination: file
    daprdLogDestination: file
    metricsPort: 6095
    
    # 健康检查配置
    appHealthCheckPath: "/health"
    appHealthProbeInterval: 5
    appHealthProbeTimeout: 3
    appHealthThreshold: 3